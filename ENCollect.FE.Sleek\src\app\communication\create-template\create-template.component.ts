import { Component, inject, TemplateRef, <PERSON>Child, OnInit, NO_ERRORS_SCHEMA } from "@angular/core";
import {
  FormArray,
  FormBuilder,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
  Validators,
} from "@angular/forms";
import { ActivatedRoute, Router } from "@angular/router";
import { BsModalRef, BsModalService } from "ngx-bootstrap/modal";
import { ToastrService } from "ngx-toastr";
import { SharedModule } from "src/app/shared";
import { TemplateVarConfigDirective } from "src/app/shared/directives/template-var-config.directive";
import { TemplatePreviewDirective } from "src/app/shared/directives/template-preview.directive";
import { RestrictHtmlTagsDirective } from "src/app/shared/directives/restrict-html-tags.directive";
import { TemplateService } from "../template.service";
import { NgxEditorComponent, NgxEditorMenuComponent, Editor } from 'ngx-editor';
import TurndownService from 'turndown';

@Component({
  selector: "app-create-template",
  standalone: true,
  imports: [
    FormsModule,
    ReactiveFormsModule,
    SharedModule,
    TemplateVarConfigDirective,
    TemplatePreviewDirective,
    RestrictHtmlTagsDirective,
    NgxEditorComponent, NgxEditorMenuComponent,
  ],
  schemas: [NO_ERRORS_SCHEMA],
  providers: [TemplateService],
  templateUrl: "./create-template.component.html",
  styleUrl: "./create-template.component.scss",
})
export class CreateTemplateComponent implements OnInit {
  private fb: FormBuilder = inject(FormBuilder);
  private modalService: BsModalService = inject(BsModalService);
  private route: ActivatedRoute = inject(ActivatedRoute);
  private router: Router = inject(Router);
  private templateService: TemplateService = inject(TemplateService);
  private toastr: ToastrService = inject(ToastrService);

  breadcrumbData = [
    { label: "Communication" },
    { label: "Create Communication Template" },
  ];

  isViewMode: boolean = false;
  isEditMode: boolean = false;
  templateId: string | null = null;
  isLoading: boolean = false;
  templateData: any = null;
  currentTemplateData: any = null;
  selectedViewLanguageIndex: number = 0;
  allLanguages: any[] = [];
  createForm!: FormGroup;
  variables: {name: string, id: string, code?: string}[] = [];
  isLoadingFields = false;
  selectedVariable: {value: string, index: number} = { value: null, index: -1 };
  selectedLanguage: string = null;
  mapVarModalRef!: BsModalRef;
  addLangModalRef!: BsModalRef;
  @ViewChild('templateVarConfig', { static: false }) templateVarConfig!: TemplateVarConfigDirective;

  html = '';
  html2 = ''
  html3 = ''

  editor: Editor;
  editor2: Editor;
  editor3: Editor;
  turndownService = new TurndownService();

  constructor() {
    this.buildCreateTemplateForm();
    this.editor = new Editor();
    this.editor2 = new Editor();
    this.editor3 = new Editor();
  }

  ngOnInit() {
    // Initialize editors
    this.editor = new Editor();
    this.editor2 = new Editor();
    this.editor3 = new Editor();
    
    // Initialize TurndownService with options
    this.turndownService = new TurndownService({
      headingStyle: 'atx',
      hr: '---',
      bulletListMarker: '-',
      codeBlockStyle: 'fenced',
      emDelimiter: '*',
      strongDelimiter: '**'
    });
    
    this.loadDatabaseFields();
    this.loadLanguageList();
    this.route.params.subscribe(params => {
      this.templateId = params['id'] || null;

      const url = this.router.url;
      if (url.includes('view-communication-template')) {
        this.isViewMode = true;
        this.isEditMode = false;
        this.updateBreadcrumb('View Communication Template');
      } else if (url.includes('edit-communication-template')) {
        this.isViewMode = false;
        this.isEditMode = true;
        this.updateBreadcrumb('Edit Communication Template');
      } else {
        this.isViewMode = false;
        this.isEditMode = false;
        this.updateBreadcrumb('Create Communication Template');
      }

      if (this.templateId && (this.isViewMode || this.isEditMode)) {
        this.loadTemplateData(this.templateId);
      }
    });

    // Initialize letter header with sample content for demo
   
  }

  private updateBreadcrumb(label: string) {
    this.breadcrumbData = [
      { label: "Communication" },
      { label: label },
    ];
  }

  private loadTemplateData(templateId: string) {
    this.isLoading = true;

    if (this.allLanguages.length === 0) {
      this.templateService.languageList().subscribe({
        next: (languageResponse: any) => {
          this.mapLanguageResponse(languageResponse);
          this.fetchAndPopulateTemplate(templateId);
        },
        error: () => {
          this.allLanguages = [{ name: "English", code: "en" }];
          this.fetchAndPopulateTemplate(templateId);
        }
      });
    } else {
      this.fetchAndPopulateTemplate(templateId);
    }
  }

  private fetchAndPopulateTemplate(templateId: string) {
    this.templateService.fetchTemplateById(templateId).subscribe({
      next: (response: any) => {
        this.templateData = response;
        this.populateFormWithTemplateData(response);
        this.isLoading = false;
      },
      error: (error) => {
        this.isLoading = false;
        this.toastr.error(error, 'Error!');
      }
    });
  }

  get isSMS(): boolean {
  return this.createForm?.get('channelType')?.value?.toLowerCase() === 'sms';
}


  private populateFormWithTemplateData(apiResponse: any) {
    if (!apiResponse) return;

    const templateData = apiResponse.data || apiResponse;
    const templateDetails = templateData.communicationTemplateDetails || [];
    this.currentTemplateData = templateData;

    // Convert the boolean value to string "true" or "false" for radio buttons
    const allowAccessValue = templateData.isAvailableInAccountDetails === true ? "true" : "false";

    this.createForm.patchValue({
      channelType: templateData.templateType?.toLowerCase() || 'email',
      templateName: templateData.name || '',
      allowAccessFromAccount: allowAccessValue,
    });

    const languagesArray = this.createForm.get('languages') as FormArray;
    while (languagesArray.length !== 0) {
      languagesArray.removeAt(0);
    }

    if (templateDetails.length > 0) {
      templateDetails.forEach((detail: any) => {
        let languageCode = 'en';
        const languageName = detail.language || 'English';

        if (this.allLanguages && this.allLanguages.length > 0) {
          const foundLanguage = this.allLanguages.find(lang =>
            lang.name.toLowerCase() === languageName.toLowerCase()
          );
          if (foundLanguage) {
            languageCode = foundLanguage.code;
          }
        }

        const languageFormGroup = this.buildLanguageFormGroup({
          code: languageCode,
          name: languageName
        });

        languageFormGroup.patchValue({
          languageCode: languageCode,
          languageName: languageName,
          emailSubject: detail.subject || '',
          templateBody: detail.body || '',
        });
        languagesArray.push(languageFormGroup);
      });
    } else {
      const defaultLanguageGroup = this.buildLanguageFormGroup({
        code: 'en',
        name: 'English'
      });
      languagesArray.push(defaultLanguageGroup);
    }

    if (languagesArray.length > 0) {
      const firstLanguage = languagesArray.at(0)?.value;
      const activeLanguage = firstLanguage?.languageCode || 'en';
      this.createForm.patchValue({ activeLanguage });
    }

    setTimeout(() => {
      if (this.isViewMode) {
        this.createForm.disable();
      } else if (this.isEditMode) {
        this.createForm.get('channelType')?.disable();
        this.createForm.get('templateName')?.disable();
      }
    }, 200);

    if (this.isViewMode) {
      this.updateBreadcrumb('View Communication Template');
      this.selectedViewLanguageIndex = 0;
    } else if (this.isEditMode) {
      this.updateBreadcrumb('Edit Communication Template');
    }
  }


  private loadDatabaseFields() {
    this.isLoadingFields = true;

    this.templateService.getFieldsList().subscribe({
      next: (response: any) => {
        this.mapFieldsToVariables(response);
        this.isLoadingFields = false;
      },
      error: () => {
        this.isLoadingFields = false;
        this.toastr.error('error', 'Error!');
        this.variables = [];
      }
    });
  }

  private mapFieldsToVariables(apiResponse: any[]) {
    if (!Array.isArray(apiResponse)) {
      this.variables = [];
      return;
    }

    this.variables = apiResponse.map(field => ({
      name: field.name || field.fieldName || 'Unknown Field',
      id: field.id || field.code || field.name?.toUpperCase().replace(/\s+/g, '_') || 'UNKNOWN',
      code: field.code || field.id || 'UNKNOWN_CODE'
    }));
  }

  buildCreateTemplateForm() {
    this.createForm = this.fb.group({
      channelType: ["email", [Validators.required]],
      templateName: [null, [Validators.required]],
      allowAccessFromAccount: ["true"],
      activeLanguage: ["en"],
      letterHeader: [''], // This will store the Markdown content
      letterFooter: [''],
      languages: this.fb.array([]),

    });

    this.createForm.get('channelType')?.valueChanges.subscribe(channelType => {
      this.updateLanguageValidation(channelType);
    });

    this.updateLanguageValidation(this.createForm.get('channelType')?.value);
  }

  private updateLanguageValidation(channelType: string) {
    const languagesArray = this.createForm.get('languages') as FormArray;

    languagesArray.controls.forEach(languageControl => {
      const emailSubjectControl = languageControl.get('emailSubject');

      if (channelType === 'email') {
        emailSubjectControl?.setValidators([Validators.required]);
      } else {
        emailSubjectControl?.clearValidators();
      }

      emailSubjectControl?.updateValueAndValidity();
    });
  }

  buildLanguagesFormArray(data?: any[]) {
    const formArray = new FormArray([]);

    if (data && data.length > 0) {
      data.forEach((o) => {
        formArray.push(this.buildLanguageFormGroup(o));
      });
    } else if (this.allLanguages && this.allLanguages.length > 0) {
      formArray.push(this.buildLanguageFormGroup(this.allLanguages[0]));
    }

    return formArray;
  }

  buildLanguageFormGroup(data?: any) {
    return this.fb.group({
      languageCode: data?.code,
      languageName: data?.name,
      emailSubject: [null],
      templateBody: [null, [Validators.required]],
    });
  }

  get fValue(): any {
    return this.createForm.value;
  }

  shouldShowLanguageFields(languageIndex: number): boolean {
    if (this.isViewMode) {
      return languageIndex === 0;
    }
    const activeLanguage = this.fValue?.activeLanguage;
    const currentLanguage = this.fValue?.languages?.[languageIndex];
    return activeLanguage === currentLanguage?.languageCode;
  }

  selectViewLanguage(index: number): void {
    this.selectedViewLanguageIndex = index;
  }

  getSelectedLanguageDetail(): any {
    if (this.isViewMode && this.currentTemplateData?.communicationTemplateDetails?.length > 0) {
      return this.currentTemplateData.communicationTemplateDetails[this.selectedViewLanguageIndex] ||
             this.currentTemplateData.communicationTemplateDetails[0];
    }
    return null;
  }

  getFormValidationErrors() {
    let formErrors: any = {};

    Object.keys(this.createForm.controls).forEach(key => {
      const controlErrors = this.createForm.get(key)?.errors;
      if (controlErrors) {
        formErrors[key] = controlErrors;
      }
    });

    const languagesArray = this.createForm.get('languages') as FormArray;
    if (languagesArray) {
      languagesArray.controls.forEach((control, index) => {
        const formGroup = control as FormGroup;
        Object.keys(formGroup.controls).forEach(fieldKey => {
          const fieldControl = formGroup.get(fieldKey);
          if (fieldControl?.errors) {
            if (!formErrors.languages) formErrors.languages = {};
            if (!formErrors.languages[index]) formErrors.languages[index] = {};
            formErrors.languages[index][fieldKey] = fieldControl.errors;
          }
        });
      });
    }

    return formErrors;
  }

  openMapVariableModal(event: {index: number, value: string}, template: TemplateRef<any>) {
    this.selectedVariable = event;
    this.mapVarModalRef = this.modalService.show(template, {
      animated: true,
    })
  }

  assignVariable() {
    this.mapVarModalRef.hide();
    if (this.selectedVariable?.value) {
      this.templateVarConfig.onUpdateVariable(this.selectedVariable);
      this.selectedVariable = { value: null, index: -1 };
    }
  }

  updateTemplateValue(template: string, index: number) {
    const languageControl = (this.createForm.get('languages') as FormArray).at(index);
    languageControl.patchValue({ templateBody: template });
    languageControl.get('templateBody')?.updateValueAndValidity();
    this.createForm.updateValueAndValidity();
  }

  openAddLangModal(template: TemplateRef<any>) {
    this.addLangModalRef = this.modalService.show(template, { animated: true });
  }

  addLanguage() {
    this.addLangModalRef?.hide();
    const language = this.allLanguages.find(o => o?.code === this.selectedLanguage);

    const languagesArray = this.createForm.get('languages') as FormArray;
    const isLanguageAlreadySelected = languagesArray.controls.some(control =>
      control.get('languageCode')?.value === this.selectedLanguage
    );

    if (isLanguageAlreadySelected) {
      this.toastr.error('The same language is already selected.', 'Error!');
      return;
    }

    const langFormGroup = this.buildLanguageFormGroup(language);
    languagesArray.push(langFormGroup);
    this.updateLanguageValidation(this.createForm.get('channelType')?.value);
    this.createForm.updateValueAndValidity();
  }

  removeLanguage(index: number) {
    const languagesArray = this.createForm.get('languages') as FormArray;

    if (languagesArray.length <= 1) {
      return;
    }

    const removedLanguage = languagesArray.at(index).value;
    languagesArray.removeAt(index);

    if (this.fValue?.activeLanguage === removedLanguage?.languageCode) {
      const firstLanguage = languagesArray.at(0)?.value;
      if (firstLanguage) {
        this.createForm.patchValue({ activeLanguage: firstLanguage.languageCode });
      }
    }

    this.createForm.updateValueAndValidity();
  }

  createTemplate() {
    // this.markFormGroupTouched(this.createForm);
    console.log('Letter Header Content:', this.html);
    console.log('Letter Footer Content:', this.html2);
    console.log('Full Form Value:', this.createForm.value);

    // if (this.createForm.invalid) {
    //   this.toastr.error('Please fill all required fields.', 'Error');
    //   return;
    // }

    // const formValue = this.createForm.value;
    // const hasTemplateBody = formValue.languages?.some((lang: any) => lang.templateBody && lang.templateBody.trim());

    // if (!hasTemplateBody) {
    //   this.toastr.error('Please enter template body content.', 'Error');
    //   return;
    // }

    // if (this.hasUnmappedVariables(formValue.languages)) {
    //   this.toastr.error('Please map all variables and continue creating template.', 'Error');
    //   return;
    // }

    // if (formValue.channelType === 'email') {
    //   const hasEmailSubject = formValue.languages?.every((lang: any) => lang.emailSubject && lang.emailSubject.trim());
    //   if (!hasEmailSubject) {
    //     this.toastr.error('Please enter email subject for all languages.', 'Error');
    //     return;
    //   }
    // }

    // const getTemplateType = (channelType: string): string => {
    //   switch (channelType.toLowerCase()) {
    //     case 'email': return 'Email';
    //     case 'sms': return 'SMS';
    //     case 'letter': return 'Letter';
    //     default: return 'Email';
    //   }
    // };

    // const getLanguageName = (languageCode: string): string => {
    //   const language = this.allLanguages.find(lang => lang.code === languageCode);
    //   return language ? language.name : 'English';
    // };

    // const communicationTemplateDetails = formValue.languages.map((lang: any) => {
    //   const detail: any = {
    //     "Language": getLanguageName(lang.languageCode),
    //     "body": lang.templateBody || ""
    //   };

    //   if (formValue.channelType === 'email') {
    //     detail.Subject = lang.emailSubject || "";
    //   }

    //   return detail;
    // });

    // const json = {
    //   "templateType": getTemplateType(formValue.channelType),
    //   "Name": formValue.templateName,
    //   "isAvailableInAccountDetails": formValue.allowAccessFromAccount === 'true',
    //   "CommunicationTemplateDetails": communicationTemplateDetails
    // };

    // this.templateService.saveCommunicationTemplate(json).subscribe({
    //   next: () => {
    //     this.toastr.success(`The Template "${json.Name}" has been created successfully.`, "Success!");
    //     this.router.navigate(['communication/search-communication-templates']);
    //   },
    //   error: (error) => {
    //     this.toastr.error(error, "Error!");
    //   }
    // });
  }

  updateTemplate() {
    this.markFormGroupTouched(this.createForm);

    if (this.createForm.invalid) {
      this.toastr.error('Please fill in all required fields.', 'Error');
      return;
    }

    const formValue = this.createForm.getRawValue();
    const hasTemplateBody = formValue.languages?.some((lang: any) => lang.templateBody && lang.templateBody.trim());

    if (!hasTemplateBody) {
      this.toastr.error('Please enter template body content.', 'Error');
      return;
    }

    if (this.hasUnmappedVariables(formValue.languages)) {
      this.toastr.error('Please map all variables and continue creating template.', 'Error');
      return;
    }

    if (!formValue.channelType) {
      this.toastr.error('Please select a channel type.', 'Error');
      return;
    }

    if (formValue.channelType === 'email') {
      const hasEmailSubject = formValue.languages?.every((lang: any) => lang.emailSubject && lang.emailSubject.trim());
      if (!hasEmailSubject) {
        this.toastr.error('Please enter email subject for all languages.', 'Error');
        return;
      }
    }

    const getTemplateType = (channelType: string): string => {
      if (!channelType || typeof channelType !== 'string') {
        return 'Email';
      }
      switch (channelType.toLowerCase()) {
        case 'email': return 'Email';
        case 'sms': return 'SMS';
        case 'letter': return 'Letter';
        default: return 'Email';
      }
    };

    const getLanguageName = (languageCode: string): string => {
      if (!languageCode) return 'English';
      const language = this.allLanguages.find(lang => lang.code === languageCode);
      return language ? language.name : 'English';
    };

    const communicationTemplateDetails = formValue.languages?.map((lang: any) => {
      const detail: any = {
        "Language": getLanguageName(lang.languageCode),
        "body": lang.templateBody || ""
      };

      if (formValue.channelType === 'email') {
        detail.Subject = lang.emailSubject || "";
      }

      return detail;
    }) || [];

    const json = {
      "id": this.templateId,
      "templateType": getTemplateType(formValue.channelType),
      "Name": formValue.templateName || "",
      "IsAvailableInAccountDetails": formValue.allowAccessFromAccount === 'true',
      "CommunicationTemplateDetails": communicationTemplateDetails
    };

    this.templateService.updateCcmTemplate(json).subscribe({
      next: () => {
        this.toastr.success(`The Template "${json.Name}" has been updated successfully.`, "Success!");
        this.router.navigate(['communication/search-communication-templates']);
      },
      error: (error) => {
        this.toastr.error(error, "Error!");
      }
    });
  }

  private loadLanguageList() {
    this.templateService.languageList().subscribe({
      next: (response: any) => {
        this.mapLanguageResponse(response);
      },
      error: (error) => {
        this.toastr.error(error, 'Error');
        this.allLanguages = [{ name: "English", code: "en" }];
        this.initializeDefaultLanguage();
      }
    });
  }

  private mapLanguageResponse(apiResponse: any) {
    if (!apiResponse || !Array.isArray(apiResponse)) {
      this.allLanguages = [{ name: "English", code: "en" }];
      this.initializeDefaultLanguage();
      return;
    }

    this.allLanguages = apiResponse.map(lang => ({
      name: lang.name || lang.itemName || 'Unknown Language',
      code: lang.code || lang.itemCode || lang.name?.toLowerCase().substring(0, 2) || 'en'
    }));

    if (this.allLanguages.length === 0) {
      this.allLanguages = [{ name: "English", code: "en" }];
    }

    this.initializeDefaultLanguage();
  }

  private initializeDefaultLanguage() {
    if (this.createForm && this.allLanguages.length > 0) {
      const currentActiveLanguage = this.createForm.get('activeLanguage')?.value;
      if (!currentActiveLanguage || !this.allLanguages.find(lang => lang.code === currentActiveLanguage)) {
        this.createForm.patchValue({ activeLanguage: this.allLanguages[0].code });

        const languagesArray = this.createForm.get('languages') as FormArray;
        if (languagesArray.length === 0) {
          const defaultLanguageGroup = this.buildLanguageFormGroup(this.allLanguages[0]);
          languagesArray.push(defaultLanguageGroup);
        }
      }
    }
  }

  private markFormGroupTouched(formGroup: FormGroup | FormArray): void {
    Object.keys(formGroup.controls).forEach(key => {
      const control = formGroup.get(key);
      if (control instanceof FormGroup || control instanceof FormArray) {
        this.markFormGroupTouched(control);
      } else {
        control?.markAsTouched();
      }
    });
  }

  isFieldInvalid(fieldName: string): boolean {
    const field = this.createForm.get(fieldName);
    return !!(field && field.invalid && (field.dirty || field.touched));
  }

  getFieldErrorMessage(fieldName: string): string {
    const field = this.createForm.get(fieldName);
    if (field && field.errors && (field.dirty || field.touched)) {
      if (field.errors['required']) {
        return `${this.getFieldDisplayName(fieldName)} is required`;
      }
    }
    return '';
  }

  isLanguageFieldInvalid(languageIndex: number, fieldName: string): boolean {
    const languagesArray = this.createForm.get('languages') as FormArray;
    const languageGroup = languagesArray.at(languageIndex) as FormGroup;
    const field = languageGroup?.get(fieldName);
    return !!(field && field.invalid && (field.dirty || field.touched));
  }

  getLanguageFieldErrorMessage(languageIndex: number, fieldName: string): string {
    const languagesArray = this.createForm.get('languages') as FormArray;
    const languageGroup = languagesArray.at(languageIndex) as FormGroup;
    const field = languageGroup?.get(fieldName);
    if (field && field.errors && (field.dirty || field.touched)) {
      if (field.errors['required']) {
        return `${this.getFieldDisplayName(fieldName)} is required`;
      }
    }
    return '';
  }

  private hasUnmappedVariables(languages: any[]): boolean {
    if (!languages || languages.length === 0) {
      return false;
    }

    return languages.some(lang => {
      const templateBody = lang.templateBody || '';
      return templateBody.includes('<<Var>>');
    });
  }

  private getFieldDisplayName(fieldName: string): string {
    const fieldNames: { [key: string]: string } = {
      'templateName': 'Template Name',
      'channelType': 'Channel Type',
      'emailSubject': 'Subject Line',
      'templateBody': 'Template Body'
    };
    return fieldNames[fieldName] || fieldName;
  }

  insertVariable() {
    const activeLanguageIndex = this.getActiveLanguageIndex();
    if (activeLanguageIndex === -1) return;

    const control = (this.createForm.get('languages') as FormArray).at(activeLanguageIndex).get('templateBody');
    const currentValue = control.value || '';
  
    const textareaElement = document.getElementById('templateBody') as HTMLTextAreaElement;
    if (!textareaElement) return;
  
    const cursorPosition = textareaElement.selectionStart;
    
   
    const newValue = currentValue.substring(0, cursorPosition) + 
                    '<<Var>>' + 
                    currentValue.substring(textareaElement.selectionEnd || cursorPosition);
  
    control.setValue(newValue);
    control.updateValueAndValidity();
    setTimeout(() => {
      textareaElement.focus();
      textareaElement.setSelectionRange(cursorPosition + 7, cursorPosition + 7);
    }, 0);
  }

  getActiveLanguageIndex(): number {
    const activeLanguage = this.createForm.get('activeLanguage').value;
    const languages = this.createForm.get('languages') as FormArray;

    for (let i = 0; i < languages.length; i++) {
      const lang = languages.at(i).get('languageCode').value;
      if (lang === activeLanguage) {
        return i;
      }
    }
    return -1;
  }

  shouldRestrictHtmlTags(): boolean {
    return this.createForm?.get('channelType')?.value !== 'sms';
  }

  // Method to convert HTML to Markdown when editor content changes
  onEditorContentChange() {
  const markdown = this.turndownService.turndown(this.html);
  this.createForm.patchValue({
    letterHeader: markdown
  });
}

  // Method to convert specific HTML example to Markdown
  convertExampleHtml() {
  const exampleHtml = '<p><strong>Hello</strong>, this is <em>sample</em> content.</p>';
  const markdownResult = this.turndownService.turndown(exampleHtml);

  console.log('Example HTML:', exampleHtml);
  console.log('Converted Markdown:', markdownResult);

  this.html = exampleHtml;
  this.createForm.patchValue({
    letterHeader: markdownResult
  });

  return markdownResult;
}


  // Add this method to get markdown content
  getMarkdownContent(htmlContent: string): string {
    // Simple HTML to Markdown conversion for common elements
    let markdown = htmlContent;
  
    // Replace paragraph tags
    markdown = markdown.replace(/<p>(.*?)<\/p>/g, '$1\n\n');
  
    // Replace strong/bold tags
    markdown = markdown.replace(/<strong>(.*?)<\/strong>/g, '**$1**');
  
    // Replace emphasis/italic tags
    markdown = markdown.replace(/<em>(.*?)<\/em>/g, '*$1*');
  
    // Replace links
    markdown = markdown.replace(/<a href="(.*?)">(.*?)<\/a>/g, '[$2]($1)');
  
    // Replace headers
    markdown = markdown.replace(/<h1>(.*?)<\/h1>/g, '# $1\n');
    markdown = markdown.replace(/<h2>(.*?)<\/h2>/g, '## $1\n');
    markdown = markdown.replace(/<h3>(.*?)<\/h3>/g, '### $1\n');
  
    // Replace line breaks
    markdown = markdown.replace(/<br\s*\/?>/g, '\n');
  
    return markdown;
  }

}
