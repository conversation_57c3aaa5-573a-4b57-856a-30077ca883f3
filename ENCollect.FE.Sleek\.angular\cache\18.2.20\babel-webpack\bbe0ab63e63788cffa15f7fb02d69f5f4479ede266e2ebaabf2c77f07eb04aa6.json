{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./create-template.component.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"./create-template.component.scss?ngResource\";\nimport { Component, inject, ViewChild, NO_ERRORS_SCHEMA } from \"@angular/core\";\nimport { FormArray, FormBuilder, FormGroup, FormsModule, ReactiveFormsModule, Validators } from \"@angular/forms\";\nimport { ActivatedRoute, Router } from \"@angular/router\";\nimport { BsModalService } from \"ngx-bootstrap/modal\";\nimport { ToastrService } from \"ngx-toastr\";\nimport { SharedModule } from \"src/app/shared\";\nimport { TemplateVarConfigDirective } from \"src/app/shared/directives/template-var-config.directive\";\nimport { TemplatePreviewDirective } from \"src/app/shared/directives/template-preview.directive\";\nimport { RestrictHtmlTagsDirective } from \"src/app/shared/directives/restrict-html-tags.directive\";\nimport { TemplateService } from \"../template.service\";\nimport { NgxEditorComponent, NgxEditorMenuComponent, Editor } from 'ngx-editor';\nimport TurndownService from 'turndown';\nlet CreateTemplateComponent = class CreateTemplateComponent {\n  constructor() {\n    this.fb = inject(FormBuilder);\n    this.modalService = inject(BsModalService);\n    this.route = inject(ActivatedRoute);\n    this.router = inject(Router);\n    this.templateService = inject(TemplateService);\n    this.toastr = inject(ToastrService);\n    this.breadcrumbData = [{\n      label: \"Communication\"\n    }, {\n      label: \"Create Communication Template\"\n    }];\n    this.isViewMode = false;\n    this.isEditMode = false;\n    this.templateId = null;\n    this.isLoading = false;\n    this.templateData = null;\n    this.currentTemplateData = null;\n    this.selectedViewLanguageIndex = 0;\n    this.allLanguages = [];\n    this.variables = [];\n    this.isLoadingFields = false;\n    this.selectedVariable = {\n      value: null,\n      index: -1\n    };\n    this.selectedLanguage = null;\n    this.html = '';\n    this.html2 = '';\n    this.html3 = '';\n    this.turndownService = new TurndownService();\n    this.buildCreateTemplateForm();\n    this.editor = new Editor();\n    this.editor2 = new Editor();\n    this.editor3 = new Editor();\n  }\n  ngOnInit() {\n    // Initialize editors\n    this.editor = new Editor();\n    this.editor2 = new Editor();\n    this.editor3 = new Editor();\n    // Initialize TurndownService with options\n    this.turndownService = new TurndownService({\n      headingStyle: 'atx',\n      hr: '---',\n      bulletListMarker: '-',\n      codeBlockStyle: 'fenced',\n      emDelimiter: '*',\n      strongDelimiter: '**'\n    });\n    this.loadDatabaseFields();\n    this.loadLanguageList();\n    this.route.params.subscribe(params => {\n      this.templateId = params['id'] || null;\n      const url = this.router.url;\n      if (url.includes('view-communication-template')) {\n        this.isViewMode = true;\n        this.isEditMode = false;\n        this.updateBreadcrumb('View Communication Template');\n      } else if (url.includes('edit-communication-template')) {\n        this.isViewMode = false;\n        this.isEditMode = true;\n        this.updateBreadcrumb('Edit Communication Template');\n      } else {\n        this.isViewMode = false;\n        this.isEditMode = false;\n        this.updateBreadcrumb('Create Communication Template');\n      }\n      if (this.templateId && (this.isViewMode || this.isEditMode)) {\n        this.loadTemplateData(this.templateId);\n      }\n    });\n    // Initialize letter header with sample content for demo\n  }\n  updateBreadcrumb(label) {\n    this.breadcrumbData = [{\n      label: \"Communication\"\n    }, {\n      label: label\n    }];\n  }\n  loadTemplateData(templateId) {\n    this.isLoading = true;\n    if (this.allLanguages.length === 0) {\n      this.templateService.languageList().subscribe({\n        next: languageResponse => {\n          this.mapLanguageResponse(languageResponse);\n          this.fetchAndPopulateTemplate(templateId);\n        },\n        error: () => {\n          this.allLanguages = [{\n            name: \"English\",\n            code: \"en\"\n          }];\n          this.fetchAndPopulateTemplate(templateId);\n        }\n      });\n    } else {\n      this.fetchAndPopulateTemplate(templateId);\n    }\n  }\n  fetchAndPopulateTemplate(templateId) {\n    this.templateService.fetchTemplateById(templateId).subscribe({\n      next: response => {\n        this.templateData = response;\n        this.populateFormWithTemplateData(response);\n        this.isLoading = false;\n      },\n      error: error => {\n        this.isLoading = false;\n        this.toastr.error(error, 'Error!');\n      }\n    });\n  }\n  get isSMS() {\n    return this.createForm?.get('channelType')?.value?.toLowerCase() === 'sms';\n  }\n  populateFormWithTemplateData(apiResponse) {\n    if (!apiResponse) return;\n    const templateData = apiResponse.data || apiResponse;\n    const templateDetails = templateData.communicationTemplateDetails || [];\n    this.currentTemplateData = templateData;\n    // Convert the boolean value to string \"true\" or \"false\" for radio buttons\n    const allowAccessValue = templateData.isAvailableInAccountDetails === true ? \"true\" : \"false\";\n    this.createForm.patchValue({\n      channelType: templateData.templateType?.toLowerCase() || 'email',\n      templateName: templateData.name || '',\n      allowAccessFromAccount: allowAccessValue\n    });\n    const languagesArray = this.createForm.get('languages');\n    while (languagesArray.length !== 0) {\n      languagesArray.removeAt(0);\n    }\n    if (templateDetails.length > 0) {\n      templateDetails.forEach(detail => {\n        let languageCode = 'en';\n        const languageName = detail.language || 'English';\n        if (this.allLanguages && this.allLanguages.length > 0) {\n          const foundLanguage = this.allLanguages.find(lang => lang.name.toLowerCase() === languageName.toLowerCase());\n          if (foundLanguage) {\n            languageCode = foundLanguage.code;\n          }\n        }\n        const languageFormGroup = this.buildLanguageFormGroup({\n          code: languageCode,\n          name: languageName\n        });\n        languageFormGroup.patchValue({\n          languageCode: languageCode,\n          languageName: languageName,\n          emailSubject: detail.subject || '',\n          templateBody: detail.body || ''\n        });\n        languagesArray.push(languageFormGroup);\n      });\n    } else {\n      const defaultLanguageGroup = this.buildLanguageFormGroup({\n        code: 'en',\n        name: 'English'\n      });\n      languagesArray.push(defaultLanguageGroup);\n    }\n    if (languagesArray.length > 0) {\n      const firstLanguage = languagesArray.at(0)?.value;\n      const activeLanguage = firstLanguage?.languageCode || 'en';\n      this.createForm.patchValue({\n        activeLanguage\n      });\n    }\n    setTimeout(() => {\n      if (this.isViewMode) {\n        this.createForm.disable();\n      } else if (this.isEditMode) {\n        this.createForm.get('channelType')?.disable();\n        this.createForm.get('templateName')?.disable();\n      }\n    }, 200);\n    if (this.isViewMode) {\n      this.updateBreadcrumb('View Communication Template');\n      this.selectedViewLanguageIndex = 0;\n    } else if (this.isEditMode) {\n      this.updateBreadcrumb('Edit Communication Template');\n    }\n  }\n  loadDatabaseFields() {\n    this.isLoadingFields = true;\n    this.templateService.getFieldsList().subscribe({\n      next: response => {\n        this.mapFieldsToVariables(response);\n        this.isLoadingFields = false;\n      },\n      error: () => {\n        this.isLoadingFields = false;\n        this.toastr.error('error', 'Error!');\n        this.variables = [];\n      }\n    });\n  }\n  mapFieldsToVariables(apiResponse) {\n    if (!Array.isArray(apiResponse)) {\n      this.variables = [];\n      return;\n    }\n    this.variables = apiResponse.map(field => ({\n      name: field.name || field.fieldName || 'Unknown Field',\n      id: field.id || field.code || field.name?.toUpperCase().replace(/\\s+/g, '_') || 'UNKNOWN',\n      code: field.code || field.id || 'UNKNOWN_CODE'\n    }));\n  }\n  buildCreateTemplateForm() {\n    this.createForm = this.fb.group({\n      channelType: [\"email\", [Validators.required]],\n      templateName: [null, [Validators.required]],\n      allowAccessFromAccount: [\"true\"],\n      activeLanguage: [\"en\"],\n      letterHeader: [''],\n      // This will store the Markdown content\n      letterFooter: [''],\n      languages: this.fb.array([])\n    });\n    this.createForm.get('channelType')?.valueChanges.subscribe(channelType => {\n      this.updateLanguageValidation(channelType);\n    });\n    this.updateLanguageValidation(this.createForm.get('channelType')?.value);\n  }\n  updateLanguageValidation(channelType) {\n    const languagesArray = this.createForm.get('languages');\n    languagesArray.controls.forEach(languageControl => {\n      const emailSubjectControl = languageControl.get('emailSubject');\n      if (channelType === 'email') {\n        emailSubjectControl?.setValidators([Validators.required]);\n      } else {\n        emailSubjectControl?.clearValidators();\n      }\n      emailSubjectControl?.updateValueAndValidity();\n    });\n  }\n  buildLanguagesFormArray(data) {\n    const formArray = new FormArray([]);\n    if (data && data.length > 0) {\n      data.forEach(o => {\n        formArray.push(this.buildLanguageFormGroup(o));\n      });\n    } else if (this.allLanguages && this.allLanguages.length > 0) {\n      formArray.push(this.buildLanguageFormGroup(this.allLanguages[0]));\n    }\n    return formArray;\n  }\n  buildLanguageFormGroup(data) {\n    return this.fb.group({\n      languageCode: data?.code,\n      languageName: data?.name,\n      emailSubject: [null],\n      templateBody: [null, [Validators.required]]\n    });\n  }\n  get fValue() {\n    return this.createForm.value;\n  }\n  shouldShowLanguageFields(languageIndex) {\n    if (this.isViewMode) {\n      return languageIndex === 0;\n    }\n    const activeLanguage = this.fValue?.activeLanguage;\n    const currentLanguage = this.fValue?.languages?.[languageIndex];\n    return activeLanguage === currentLanguage?.languageCode;\n  }\n  selectViewLanguage(index) {\n    this.selectedViewLanguageIndex = index;\n  }\n  getSelectedLanguageDetail() {\n    if (this.isViewMode && this.currentTemplateData?.communicationTemplateDetails?.length > 0) {\n      return this.currentTemplateData.communicationTemplateDetails[this.selectedViewLanguageIndex] || this.currentTemplateData.communicationTemplateDetails[0];\n    }\n    return null;\n  }\n  getFormValidationErrors() {\n    let formErrors = {};\n    Object.keys(this.createForm.controls).forEach(key => {\n      const controlErrors = this.createForm.get(key)?.errors;\n      if (controlErrors) {\n        formErrors[key] = controlErrors;\n      }\n    });\n    const languagesArray = this.createForm.get('languages');\n    if (languagesArray) {\n      languagesArray.controls.forEach((control, index) => {\n        const formGroup = control;\n        Object.keys(formGroup.controls).forEach(fieldKey => {\n          const fieldControl = formGroup.get(fieldKey);\n          if (fieldControl?.errors) {\n            if (!formErrors.languages) formErrors.languages = {};\n            if (!formErrors.languages[index]) formErrors.languages[index] = {};\n            formErrors.languages[index][fieldKey] = fieldControl.errors;\n          }\n        });\n      });\n    }\n    return formErrors;\n  }\n  openMapVariableModal(event, template) {\n    this.selectedVariable = event;\n    this.mapVarModalRef = this.modalService.show(template, {\n      animated: true\n    });\n  }\n  assignVariable() {\n    this.mapVarModalRef.hide();\n    if (this.selectedVariable?.value) {\n      this.templateVarConfig.onUpdateVariable(this.selectedVariable);\n      this.selectedVariable = {\n        value: null,\n        index: -1\n      };\n    }\n  }\n  updateTemplateValue(template, index) {\n    const languageControl = this.createForm.get('languages').at(index);\n    languageControl.patchValue({\n      templateBody: template\n    });\n    languageControl.get('templateBody')?.updateValueAndValidity();\n    this.createForm.updateValueAndValidity();\n  }\n  openAddLangModal(template) {\n    this.addLangModalRef = this.modalService.show(template, {\n      animated: true\n    });\n  }\n  addLanguage() {\n    this.addLangModalRef?.hide();\n    const language = this.allLanguages.find(o => o?.code === this.selectedLanguage);\n    const languagesArray = this.createForm.get('languages');\n    const isLanguageAlreadySelected = languagesArray.controls.some(control => control.get('languageCode')?.value === this.selectedLanguage);\n    if (isLanguageAlreadySelected) {\n      this.toastr.error('The same language is already selected.', 'Error!');\n      return;\n    }\n    const langFormGroup = this.buildLanguageFormGroup(language);\n    languagesArray.push(langFormGroup);\n    this.updateLanguageValidation(this.createForm.get('channelType')?.value);\n    this.createForm.updateValueAndValidity();\n  }\n  removeLanguage(index) {\n    const languagesArray = this.createForm.get('languages');\n    if (languagesArray.length <= 1) {\n      return;\n    }\n    const removedLanguage = languagesArray.at(index).value;\n    languagesArray.removeAt(index);\n    if (this.fValue?.activeLanguage === removedLanguage?.languageCode) {\n      const firstLanguage = languagesArray.at(0)?.value;\n      if (firstLanguage) {\n        this.createForm.patchValue({\n          activeLanguage: firstLanguage.languageCode\n        });\n      }\n    }\n    this.createForm.updateValueAndValidity();\n  }\n  createTemplate() {\n    // this.markFormGroupTouched(this.createForm);\n    console.log('Letter Header Content:', this.html);\n    console.log('Letter Footer Content:', this.html2);\n    console.log('Full Form Value:', this.createForm.value);\n    // if (this.createForm.invalid) {\n    //   this.toastr.error('Please fill all required fields.', 'Error');\n    //   return;\n    // }\n    // const formValue = this.createForm.value;\n    // const hasTemplateBody = formValue.languages?.some((lang: any) => lang.templateBody && lang.templateBody.trim());\n    // if (!hasTemplateBody) {\n    //   this.toastr.error('Please enter template body content.', 'Error');\n    //   return;\n    // }\n    // if (this.hasUnmappedVariables(formValue.languages)) {\n    //   this.toastr.error('Please map all variables and continue creating template.', 'Error');\n    //   return;\n    // }\n    // if (formValue.channelType === 'email') {\n    //   const hasEmailSubject = formValue.languages?.every((lang: any) => lang.emailSubject && lang.emailSubject.trim());\n    //   if (!hasEmailSubject) {\n    //     this.toastr.error('Please enter email subject for all languages.', 'Error');\n    //     return;\n    //   }\n    // }\n    // const getTemplateType = (channelType: string): string => {\n    //   switch (channelType.toLowerCase()) {\n    //     case 'email': return 'Email';\n    //     case 'sms': return 'SMS';\n    //     case 'letter': return 'Letter';\n    //     default: return 'Email';\n    //   }\n    // };\n    // const getLanguageName = (languageCode: string): string => {\n    //   const language = this.allLanguages.find(lang => lang.code === languageCode);\n    //   return language ? language.name : 'English';\n    // };\n    // const communicationTemplateDetails = formValue.languages.map((lang: any) => {\n    //   const detail: any = {\n    //     \"Language\": getLanguageName(lang.languageCode),\n    //     \"body\": lang.templateBody || \"\"\n    //   };\n    //   if (formValue.channelType === 'email') {\n    //     detail.Subject = lang.emailSubject || \"\";\n    //   }\n    //   return detail;\n    // });\n    // const json = {\n    //   \"templateType\": getTemplateType(formValue.channelType),\n    //   \"Name\": formValue.templateName,\n    //   \"isAvailableInAccountDetails\": formValue.allowAccessFromAccount === 'true',\n    //   \"CommunicationTemplateDetails\": communicationTemplateDetails\n    // };\n    // this.templateService.saveCommunicationTemplate(json).subscribe({\n    //   next: () => {\n    //     this.toastr.success(`The Template \"${json.Name}\" has been created successfully.`, \"Success!\");\n    //     this.router.navigate(['communication/search-communication-templates']);\n    //   },\n    //   error: (error) => {\n    //     this.toastr.error(error, \"Error!\");\n    //   }\n    // });\n  }\n  updateTemplate() {\n    this.markFormGroupTouched(this.createForm);\n    if (this.createForm.invalid) {\n      this.toastr.error('Please fill in all required fields.', 'Error');\n      return;\n    }\n    const formValue = this.createForm.getRawValue();\n    const hasTemplateBody = formValue.languages?.some(lang => lang.templateBody && lang.templateBody.trim());\n    if (!hasTemplateBody) {\n      this.toastr.error('Please enter template body content.', 'Error');\n      return;\n    }\n    if (this.hasUnmappedVariables(formValue.languages)) {\n      this.toastr.error('Please map all variables and continue creating template.', 'Error');\n      return;\n    }\n    if (!formValue.channelType) {\n      this.toastr.error('Please select a channel type.', 'Error');\n      return;\n    }\n    if (formValue.channelType === 'email') {\n      const hasEmailSubject = formValue.languages?.every(lang => lang.emailSubject && lang.emailSubject.trim());\n      if (!hasEmailSubject) {\n        this.toastr.error('Please enter email subject for all languages.', 'Error');\n        return;\n      }\n    }\n    const getTemplateType = channelType => {\n      if (!channelType || typeof channelType !== 'string') {\n        return 'Email';\n      }\n      switch (channelType.toLowerCase()) {\n        case 'email':\n          return 'Email';\n        case 'sms':\n          return 'SMS';\n        case 'letter':\n          return 'Letter';\n        default:\n          return 'Email';\n      }\n    };\n    const getLanguageName = languageCode => {\n      if (!languageCode) return 'English';\n      const language = this.allLanguages.find(lang => lang.code === languageCode);\n      return language ? language.name : 'English';\n    };\n    const communicationTemplateDetails = formValue.languages?.map(lang => {\n      const detail = {\n        \"Language\": getLanguageName(lang.languageCode),\n        \"body\": lang.templateBody || \"\"\n      };\n      if (formValue.channelType === 'email') {\n        detail.Subject = lang.emailSubject || \"\";\n      }\n      return detail;\n    }) || [];\n    const json = {\n      \"id\": this.templateId,\n      \"templateType\": getTemplateType(formValue.channelType),\n      \"Name\": formValue.templateName || \"\",\n      \"IsAvailableInAccountDetails\": formValue.allowAccessFromAccount === 'true',\n      \"CommunicationTemplateDetails\": communicationTemplateDetails\n    };\n    this.templateService.updateCcmTemplate(json).subscribe({\n      next: () => {\n        this.toastr.success(`The Template \"${json.Name}\" has been updated successfully.`, \"Success!\");\n        this.router.navigate(['communication/search-communication-templates']);\n      },\n      error: error => {\n        this.toastr.error(error, \"Error!\");\n      }\n    });\n  }\n  loadLanguageList() {\n    this.templateService.languageList().subscribe({\n      next: response => {\n        this.mapLanguageResponse(response);\n      },\n      error: error => {\n        this.toastr.error(error, 'Error');\n        this.allLanguages = [{\n          name: \"English\",\n          code: \"en\"\n        }];\n        this.initializeDefaultLanguage();\n      }\n    });\n  }\n  mapLanguageResponse(apiResponse) {\n    if (!apiResponse || !Array.isArray(apiResponse)) {\n      this.allLanguages = [{\n        name: \"English\",\n        code: \"en\"\n      }];\n      this.initializeDefaultLanguage();\n      return;\n    }\n    this.allLanguages = apiResponse.map(lang => ({\n      name: lang.name || lang.itemName || 'Unknown Language',\n      code: lang.code || lang.itemCode || lang.name?.toLowerCase().substring(0, 2) || 'en'\n    }));\n    if (this.allLanguages.length === 0) {\n      this.allLanguages = [{\n        name: \"English\",\n        code: \"en\"\n      }];\n    }\n    this.initializeDefaultLanguage();\n  }\n  initializeDefaultLanguage() {\n    if (this.createForm && this.allLanguages.length > 0) {\n      const currentActiveLanguage = this.createForm.get('activeLanguage')?.value;\n      if (!currentActiveLanguage || !this.allLanguages.find(lang => lang.code === currentActiveLanguage)) {\n        this.createForm.patchValue({\n          activeLanguage: this.allLanguages[0].code\n        });\n        const languagesArray = this.createForm.get('languages');\n        if (languagesArray.length === 0) {\n          const defaultLanguageGroup = this.buildLanguageFormGroup(this.allLanguages[0]);\n          languagesArray.push(defaultLanguageGroup);\n        }\n      }\n    }\n  }\n  markFormGroupTouched(formGroup) {\n    Object.keys(formGroup.controls).forEach(key => {\n      const control = formGroup.get(key);\n      if (control instanceof FormGroup || control instanceof FormArray) {\n        this.markFormGroupTouched(control);\n      } else {\n        control?.markAsTouched();\n      }\n    });\n  }\n  isFieldInvalid(fieldName) {\n    const field = this.createForm.get(fieldName);\n    return !!(field && field.invalid && (field.dirty || field.touched));\n  }\n  getFieldErrorMessage(fieldName) {\n    const field = this.createForm.get(fieldName);\n    if (field && field.errors && (field.dirty || field.touched)) {\n      if (field.errors['required']) {\n        return `${this.getFieldDisplayName(fieldName)} is required`;\n      }\n    }\n    return '';\n  }\n  isLanguageFieldInvalid(languageIndex, fieldName) {\n    const languagesArray = this.createForm.get('languages');\n    const languageGroup = languagesArray.at(languageIndex);\n    const field = languageGroup?.get(fieldName);\n    return !!(field && field.invalid && (field.dirty || field.touched));\n  }\n  getLanguageFieldErrorMessage(languageIndex, fieldName) {\n    const languagesArray = this.createForm.get('languages');\n    const languageGroup = languagesArray.at(languageIndex);\n    const field = languageGroup?.get(fieldName);\n    if (field && field.errors && (field.dirty || field.touched)) {\n      if (field.errors['required']) {\n        return `${this.getFieldDisplayName(fieldName)} is required`;\n      }\n    }\n    return '';\n  }\n  hasUnmappedVariables(languages) {\n    if (!languages || languages.length === 0) {\n      return false;\n    }\n    return languages.some(lang => {\n      const templateBody = lang.templateBody || '';\n      return templateBody.includes('<<Var>>');\n    });\n  }\n  getFieldDisplayName(fieldName) {\n    const fieldNames = {\n      'templateName': 'Template Name',\n      'channelType': 'Channel Type',\n      'emailSubject': 'Subject Line',\n      'templateBody': 'Template Body'\n    };\n    return fieldNames[fieldName] || fieldName;\n  }\n  insertVariable() {\n    const activeLanguageIndex = this.getActiveLanguageIndex();\n    if (activeLanguageIndex === -1) return;\n    const control = this.createForm.get('languages').at(activeLanguageIndex).get('templateBody');\n    const currentValue = control.value || '';\n    const textareaElement = document.getElementById('templateBody');\n    if (!textareaElement) return;\n    const cursorPosition = textareaElement.selectionStart;\n    const newValue = currentValue.substring(0, cursorPosition) + '<<Var>>' + currentValue.substring(textareaElement.selectionEnd || cursorPosition);\n    control.setValue(newValue);\n    control.updateValueAndValidity();\n    setTimeout(() => {\n      textareaElement.focus();\n      textareaElement.setSelectionRange(cursorPosition + 7, cursorPosition + 7);\n    }, 0);\n  }\n  getActiveLanguageIndex() {\n    const activeLanguage = this.createForm.get('activeLanguage').value;\n    const languages = this.createForm.get('languages');\n    for (let i = 0; i < languages.length; i++) {\n      const lang = languages.at(i).get('languageCode').value;\n      if (lang === activeLanguage) {\n        return i;\n      }\n    }\n    return -1;\n  }\n  shouldRestrictHtmlTags() {\n    return this.createForm?.get('channelType')?.value !== 'sms';\n  }\n  // Method to convert HTML to Markdown when editor content changes\n  onEditorContentChange() {\n    const markdown = this.turndownService.turndown(this.html);\n    this.createForm.patchValue({\n      letterHeader: markdown\n    });\n  }\n  // Method to convert specific HTML example to Markdown\n  convertExampleHtml() {\n    const exampleHtml = '<p><strong>Hello</strong>, this is <em>sample</em> content.</p>';\n    const markdownResult = this.turndownService.turndown(exampleHtml);\n    console.log('Example HTML:', exampleHtml);\n    console.log('Converted Markdown:', markdownResult);\n    this.html = exampleHtml;\n    this.createForm.patchValue({\n      letterHeader: markdownResult\n    });\n    return markdownResult;\n  }\n  // Add this method to get markdown content\n  getMarkdownContent(htmlContent) {\n    // Simple HTML to Markdown conversion for common elements\n    let markdown = htmlContent;\n    // Replace paragraph tags\n    markdown = markdown.replace(/<p>(.*?)<\\/p>/g, '$1\\n\\n');\n    // Replace strong/bold tags\n    markdown = markdown.replace(/<strong>(.*?)<\\/strong>/g, '**$1**');\n    // Replace emphasis/italic tags\n    markdown = markdown.replace(/<em>(.*?)<\\/em>/g, '*$1*');\n    // Replace links\n    markdown = markdown.replace(/<a href=\"(.*?)\">(.*?)<\\/a>/g, '[$2]($1)');\n    // Replace headers\n    markdown = markdown.replace(/<h1>(.*?)<\\/h1>/g, '# $1\\n');\n    markdown = markdown.replace(/<h2>(.*?)<\\/h2>/g, '## $1\\n');\n    markdown = markdown.replace(/<h3>(.*?)<\\/h3>/g, '### $1\\n');\n    // Replace line breaks\n    markdown = markdown.replace(/<br\\s*\\/?>/g, '\\n');\n    return markdown;\n  }\n  static {\n    this.ctorParameters = () => [];\n  }\n  static {\n    this.propDecorators = {\n      templateVarConfig: [{\n        type: ViewChild,\n        args: ['templateVarConfig', {\n          static: false\n        }]\n      }]\n    };\n  }\n};\nCreateTemplateComponent = __decorate([Component({\n  selector: \"app-create-template\",\n  standalone: true,\n  imports: [FormsModule, ReactiveFormsModule, SharedModule, TemplateVarConfigDirective, TemplatePreviewDirective, RestrictHtmlTagsDirective, NgxEditorComponent, NgxEditorMenuComponent],\n  schemas: [NO_ERRORS_SCHEMA],\n  providers: [TemplateService],\n  template: __NG_CLI_RESOURCE__0,\n  styles: [__NG_CLI_RESOURCE__1]\n})], CreateTemplateComponent);\nexport { CreateTemplateComponent };", "map": {"version": 3, "names": ["Component", "inject", "ViewChild", "NO_ERRORS_SCHEMA", "FormArray", "FormBuilder", "FormGroup", "FormsModule", "ReactiveFormsModule", "Validators", "ActivatedRoute", "Router", "BsModalService", "ToastrService", "SharedModule", "TemplateVarConfigDirective", "TemplatePreviewDirective", "RestrictHtmlTagsDirective", "TemplateService", "NgxEditorComponent", "NgxEditorMenuComponent", "Editor", "TurndownService", "CreateTemplateComponent", "constructor", "fb", "modalService", "route", "router", "templateService", "toastr", "breadcrumbData", "label", "isViewMode", "isEditMode", "templateId", "isLoading", "templateData", "currentTemplateData", "selectedViewLanguageIndex", "allLanguages", "variables", "isLoadingFields", "selectedVariable", "value", "index", "selectedLanguage", "html", "html2", "html3", "turndownService", "buildCreateTemplateForm", "editor", "editor2", "editor3", "ngOnInit", "headingStyle", "hr", "bullet<PERSON>ist<PERSON><PERSON><PERSON>", "codeBlockStyle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "loadDatabaseFields", "loadLanguageList", "params", "subscribe", "url", "includes", "updateBreadcrumb", "loadTemplateData", "length", "languageList", "next", "languageResponse", "mapLanguageResponse", "fetchAndPopulateTemplate", "error", "name", "code", "fetchTemplateById", "response", "populateFormWithTemplateData", "isSMS", "createForm", "get", "toLowerCase", "apiResponse", "data", "templateDetails", "communicationTemplateDetails", "allowAccessValue", "isAvailableInAccountDetails", "patchValue", "channelType", "templateType", "templateName", "allowAccessFromAccount", "languagesArray", "removeAt", "for<PERSON>ach", "detail", "languageCode", "languageName", "language", "foundLanguage", "find", "lang", "languageFormGroup", "buildLanguageFormGroup", "emailSubject", "subject", "templateBody", "body", "push", "defaultLanguageGroup", "firstLanguage", "at", "activeLanguage", "setTimeout", "disable", "getFieldsList", "mapFieldsToVariables", "Array", "isArray", "map", "field", "fieldName", "id", "toUpperCase", "replace", "group", "required", "letterHeader", "letterFooter", "languages", "array", "valueChanges", "updateLanguageValidation", "controls", "languageControl", "emailSubjectControl", "setValidators", "clearValidators", "updateValueAndValidity", "buildLanguagesFormArray", "formArray", "o", "fValue", "shouldShowLanguageFields", "languageIndex", "currentLanguage", "selectViewLanguage", "getSelectedLanguageDetail", "getFormValidationErrors", "formErrors", "Object", "keys", "key", "controlErrors", "errors", "control", "formGroup", "<PERSON><PERSON><PERSON>", "fieldControl", "openMapVariableModal", "event", "template", "mapVarModalRef", "show", "animated", "assignVariable", "hide", "templateVarConfig", "onUpdateVariable", "updateTemplateValue", "openAddLangModal", "addLangModalRef", "addLanguage", "isLanguageAlreadySelected", "some", "langFormGroup", "removeLanguage", "removedLanguage", "createTemplate", "console", "log", "updateTemplate", "markFormGroupTouched", "invalid", "formValue", "getRawValue", "hasTemplateBody", "trim", "hasUnmappedVariables", "hasEmailSubject", "every", "getTemplateType", "getLanguageName", "Subject", "json", "updateCcmTemplate", "success", "Name", "navigate", "initializeDefaultLanguage", "itemName", "itemCode", "substring", "currentActiveLanguage", "<PERSON><PERSON><PERSON><PERSON>ched", "isFieldInvalid", "dirty", "touched", "getFieldErrorMessage", "getFieldDisplayName", "isLanguageFieldInvalid", "languageGroup", "getLanguageFieldErrorMessage", "fieldNames", "insertVariable", "activeLanguageIndex", "getActiveLanguageIndex", "currentValue", "textareaElement", "document", "getElementById", "cursorPosition", "selectionStart", "newValue", "selectionEnd", "setValue", "focus", "setSelectionRange", "i", "shouldRestrictHtmlTags", "onEditorContentChange", "markdown", "turndown", "convertExampleHtml", "exampleHtml", "markdownResult", "getMarkdownContent", "htmlContent", "args", "static", "__decorate", "selector", "standalone", "imports", "schemas", "providers", "__NG_CLI_RESOURCE__0"], "sources": ["D:\\ProjectNewrepo\\Licensing\\ENCollect.FE.Sleek\\src\\app\\communication\\create-template\\create-template.component.ts"], "sourcesContent": ["import { Component, inject, TemplateRef, <PERSON>Child, OnInit, NO_ERRORS_SCHEMA } from \"@angular/core\";\nimport {\n  FormArray,\n  FormBuilder,\n  FormGroup,\n  FormsModule,\n  ReactiveFormsModule,\n  Validators,\n} from \"@angular/forms\";\nimport { ActivatedRoute, Router } from \"@angular/router\";\nimport { BsModalRef, BsModalService } from \"ngx-bootstrap/modal\";\nimport { ToastrService } from \"ngx-toastr\";\nimport { SharedModule } from \"src/app/shared\";\nimport { TemplateVarConfigDirective } from \"src/app/shared/directives/template-var-config.directive\";\nimport { TemplatePreviewDirective } from \"src/app/shared/directives/template-preview.directive\";\nimport { RestrictHtmlTagsDirective } from \"src/app/shared/directives/restrict-html-tags.directive\";\nimport { TemplateService } from \"../template.service\";\nimport { NgxEditorComponent, NgxEditorMenuComponent, Editor } from 'ngx-editor';\nimport TurndownService from 'turndown';\n\n@Component({\n  selector: \"app-create-template\",\n  standalone: true,\n  imports: [\n    FormsModule,\n    ReactiveFormsModule,\n    SharedModule,\n    TemplateVarConfigDirective,\n    TemplatePreviewDirective,\n    RestrictHtmlTagsDirective,\n    NgxEditorComponent, NgxEditorMenuComponent,\n  ],\n  schemas: [NO_ERRORS_SCHEMA],\n  providers: [TemplateService],\n  templateUrl: \"./create-template.component.html\",\n  styleUrl: \"./create-template.component.scss\",\n})\nexport class CreateTemplateComponent implements OnInit {\n  private fb: FormBuilder = inject(FormBuilder);\n  private modalService: BsModalService = inject(BsModalService);\n  private route: ActivatedRoute = inject(ActivatedRoute);\n  private router: Router = inject(Router);\n  private templateService: TemplateService = inject(TemplateService);\n  private toastr: ToastrService = inject(ToastrService);\n\n  breadcrumbData = [\n    { label: \"Communication\" },\n    { label: \"Create Communication Template\" },\n  ];\n\n  isViewMode: boolean = false;\n  isEditMode: boolean = false;\n  templateId: string | null = null;\n  isLoading: boolean = false;\n  templateData: any = null;\n  currentTemplateData: any = null;\n  selectedViewLanguageIndex: number = 0;\n  allLanguages: any[] = [];\n  createForm!: FormGroup;\n  variables: {name: string, id: string, code?: string}[] = [];\n  isLoadingFields = false;\n  selectedVariable: {value: string, index: number} = { value: null, index: -1 };\n  selectedLanguage: string = null;\n  mapVarModalRef!: BsModalRef;\n  addLangModalRef!: BsModalRef;\n  @ViewChild('templateVarConfig', { static: false }) templateVarConfig!: TemplateVarConfigDirective;\n\n  html = '';\n  html2 = ''\n  html3 = ''\n\n  editor: Editor;\n  editor2: Editor;\n  editor3: Editor;\n  turndownService = new TurndownService();\n\n  constructor() {\n    this.buildCreateTemplateForm();\n    this.editor = new Editor();\n    this.editor2 = new Editor();\n    this.editor3 = new Editor();\n  }\n\n  ngOnInit() {\n    // Initialize editors\n    this.editor = new Editor();\n    this.editor2 = new Editor();\n    this.editor3 = new Editor();\n    \n    // Initialize TurndownService with options\n    this.turndownService = new TurndownService({\n      headingStyle: 'atx',\n      hr: '---',\n      bulletListMarker: '-',\n      codeBlockStyle: 'fenced',\n      emDelimiter: '*',\n      strongDelimiter: '**'\n    });\n    \n    this.loadDatabaseFields();\n    this.loadLanguageList();\n    this.route.params.subscribe(params => {\n      this.templateId = params['id'] || null;\n\n      const url = this.router.url;\n      if (url.includes('view-communication-template')) {\n        this.isViewMode = true;\n        this.isEditMode = false;\n        this.updateBreadcrumb('View Communication Template');\n      } else if (url.includes('edit-communication-template')) {\n        this.isViewMode = false;\n        this.isEditMode = true;\n        this.updateBreadcrumb('Edit Communication Template');\n      } else {\n        this.isViewMode = false;\n        this.isEditMode = false;\n        this.updateBreadcrumb('Create Communication Template');\n      }\n\n      if (this.templateId && (this.isViewMode || this.isEditMode)) {\n        this.loadTemplateData(this.templateId);\n      }\n    });\n\n    // Initialize letter header with sample content for demo\n   \n  }\n\n  private updateBreadcrumb(label: string) {\n    this.breadcrumbData = [\n      { label: \"Communication\" },\n      { label: label },\n    ];\n  }\n\n  private loadTemplateData(templateId: string) {\n    this.isLoading = true;\n\n    if (this.allLanguages.length === 0) {\n      this.templateService.languageList().subscribe({\n        next: (languageResponse: any) => {\n          this.mapLanguageResponse(languageResponse);\n          this.fetchAndPopulateTemplate(templateId);\n        },\n        error: () => {\n          this.allLanguages = [{ name: \"English\", code: \"en\" }];\n          this.fetchAndPopulateTemplate(templateId);\n        }\n      });\n    } else {\n      this.fetchAndPopulateTemplate(templateId);\n    }\n  }\n\n  private fetchAndPopulateTemplate(templateId: string) {\n    this.templateService.fetchTemplateById(templateId).subscribe({\n      next: (response: any) => {\n        this.templateData = response;\n        this.populateFormWithTemplateData(response);\n        this.isLoading = false;\n      },\n      error: (error) => {\n        this.isLoading = false;\n        this.toastr.error(error, 'Error!');\n      }\n    });\n  }\n\n  get isSMS(): boolean {\n  return this.createForm?.get('channelType')?.value?.toLowerCase() === 'sms';\n}\n\n\n  private populateFormWithTemplateData(apiResponse: any) {\n    if (!apiResponse) return;\n\n    const templateData = apiResponse.data || apiResponse;\n    const templateDetails = templateData.communicationTemplateDetails || [];\n    this.currentTemplateData = templateData;\n\n    // Convert the boolean value to string \"true\" or \"false\" for radio buttons\n    const allowAccessValue = templateData.isAvailableInAccountDetails === true ? \"true\" : \"false\";\n\n    this.createForm.patchValue({\n      channelType: templateData.templateType?.toLowerCase() || 'email',\n      templateName: templateData.name || '',\n      allowAccessFromAccount: allowAccessValue,\n    });\n\n    const languagesArray = this.createForm.get('languages') as FormArray;\n    while (languagesArray.length !== 0) {\n      languagesArray.removeAt(0);\n    }\n\n    if (templateDetails.length > 0) {\n      templateDetails.forEach((detail: any) => {\n        let languageCode = 'en';\n        const languageName = detail.language || 'English';\n\n        if (this.allLanguages && this.allLanguages.length > 0) {\n          const foundLanguage = this.allLanguages.find(lang =>\n            lang.name.toLowerCase() === languageName.toLowerCase()\n          );\n          if (foundLanguage) {\n            languageCode = foundLanguage.code;\n          }\n        }\n\n        const languageFormGroup = this.buildLanguageFormGroup({\n          code: languageCode,\n          name: languageName\n        });\n\n        languageFormGroup.patchValue({\n          languageCode: languageCode,\n          languageName: languageName,\n          emailSubject: detail.subject || '',\n          templateBody: detail.body || '',\n        });\n        languagesArray.push(languageFormGroup);\n      });\n    } else {\n      const defaultLanguageGroup = this.buildLanguageFormGroup({\n        code: 'en',\n        name: 'English'\n      });\n      languagesArray.push(defaultLanguageGroup);\n    }\n\n    if (languagesArray.length > 0) {\n      const firstLanguage = languagesArray.at(0)?.value;\n      const activeLanguage = firstLanguage?.languageCode || 'en';\n      this.createForm.patchValue({ activeLanguage });\n    }\n\n    setTimeout(() => {\n      if (this.isViewMode) {\n        this.createForm.disable();\n      } else if (this.isEditMode) {\n        this.createForm.get('channelType')?.disable();\n        this.createForm.get('templateName')?.disable();\n      }\n    }, 200);\n\n    if (this.isViewMode) {\n      this.updateBreadcrumb('View Communication Template');\n      this.selectedViewLanguageIndex = 0;\n    } else if (this.isEditMode) {\n      this.updateBreadcrumb('Edit Communication Template');\n    }\n  }\n\n\n  private loadDatabaseFields() {\n    this.isLoadingFields = true;\n\n    this.templateService.getFieldsList().subscribe({\n      next: (response: any) => {\n        this.mapFieldsToVariables(response);\n        this.isLoadingFields = false;\n      },\n      error: () => {\n        this.isLoadingFields = false;\n        this.toastr.error('error', 'Error!');\n        this.variables = [];\n      }\n    });\n  }\n\n  private mapFieldsToVariables(apiResponse: any[]) {\n    if (!Array.isArray(apiResponse)) {\n      this.variables = [];\n      return;\n    }\n\n    this.variables = apiResponse.map(field => ({\n      name: field.name || field.fieldName || 'Unknown Field',\n      id: field.id || field.code || field.name?.toUpperCase().replace(/\\s+/g, '_') || 'UNKNOWN',\n      code: field.code || field.id || 'UNKNOWN_CODE'\n    }));\n  }\n\n  buildCreateTemplateForm() {\n    this.createForm = this.fb.group({\n      channelType: [\"email\", [Validators.required]],\n      templateName: [null, [Validators.required]],\n      allowAccessFromAccount: [\"true\"],\n      activeLanguage: [\"en\"],\n      letterHeader: [''], // This will store the Markdown content\n      letterFooter: [''],\n      languages: this.fb.array([]),\n\n    });\n\n    this.createForm.get('channelType')?.valueChanges.subscribe(channelType => {\n      this.updateLanguageValidation(channelType);\n    });\n\n    this.updateLanguageValidation(this.createForm.get('channelType')?.value);\n  }\n\n  private updateLanguageValidation(channelType: string) {\n    const languagesArray = this.createForm.get('languages') as FormArray;\n\n    languagesArray.controls.forEach(languageControl => {\n      const emailSubjectControl = languageControl.get('emailSubject');\n\n      if (channelType === 'email') {\n        emailSubjectControl?.setValidators([Validators.required]);\n      } else {\n        emailSubjectControl?.clearValidators();\n      }\n\n      emailSubjectControl?.updateValueAndValidity();\n    });\n  }\n\n  buildLanguagesFormArray(data?: any[]) {\n    const formArray = new FormArray([]);\n\n    if (data && data.length > 0) {\n      data.forEach((o) => {\n        formArray.push(this.buildLanguageFormGroup(o));\n      });\n    } else if (this.allLanguages && this.allLanguages.length > 0) {\n      formArray.push(this.buildLanguageFormGroup(this.allLanguages[0]));\n    }\n\n    return formArray;\n  }\n\n  buildLanguageFormGroup(data?: any) {\n    return this.fb.group({\n      languageCode: data?.code,\n      languageName: data?.name,\n      emailSubject: [null],\n      templateBody: [null, [Validators.required]],\n    });\n  }\n\n  get fValue(): any {\n    return this.createForm.value;\n  }\n\n  shouldShowLanguageFields(languageIndex: number): boolean {\n    if (this.isViewMode) {\n      return languageIndex === 0;\n    }\n    const activeLanguage = this.fValue?.activeLanguage;\n    const currentLanguage = this.fValue?.languages?.[languageIndex];\n    return activeLanguage === currentLanguage?.languageCode;\n  }\n\n  selectViewLanguage(index: number): void {\n    this.selectedViewLanguageIndex = index;\n  }\n\n  getSelectedLanguageDetail(): any {\n    if (this.isViewMode && this.currentTemplateData?.communicationTemplateDetails?.length > 0) {\n      return this.currentTemplateData.communicationTemplateDetails[this.selectedViewLanguageIndex] ||\n             this.currentTemplateData.communicationTemplateDetails[0];\n    }\n    return null;\n  }\n\n  getFormValidationErrors() {\n    let formErrors: any = {};\n\n    Object.keys(this.createForm.controls).forEach(key => {\n      const controlErrors = this.createForm.get(key)?.errors;\n      if (controlErrors) {\n        formErrors[key] = controlErrors;\n      }\n    });\n\n    const languagesArray = this.createForm.get('languages') as FormArray;\n    if (languagesArray) {\n      languagesArray.controls.forEach((control, index) => {\n        const formGroup = control as FormGroup;\n        Object.keys(formGroup.controls).forEach(fieldKey => {\n          const fieldControl = formGroup.get(fieldKey);\n          if (fieldControl?.errors) {\n            if (!formErrors.languages) formErrors.languages = {};\n            if (!formErrors.languages[index]) formErrors.languages[index] = {};\n            formErrors.languages[index][fieldKey] = fieldControl.errors;\n          }\n        });\n      });\n    }\n\n    return formErrors;\n  }\n\n  openMapVariableModal(event: {index: number, value: string}, template: TemplateRef<any>) {\n    this.selectedVariable = event;\n    this.mapVarModalRef = this.modalService.show(template, {\n      animated: true,\n    })\n  }\n\n  assignVariable() {\n    this.mapVarModalRef.hide();\n    if (this.selectedVariable?.value) {\n      this.templateVarConfig.onUpdateVariable(this.selectedVariable);\n      this.selectedVariable = { value: null, index: -1 };\n    }\n  }\n\n  updateTemplateValue(template: string, index: number) {\n    const languageControl = (this.createForm.get('languages') as FormArray).at(index);\n    languageControl.patchValue({ templateBody: template });\n    languageControl.get('templateBody')?.updateValueAndValidity();\n    this.createForm.updateValueAndValidity();\n  }\n\n  openAddLangModal(template: TemplateRef<any>) {\n    this.addLangModalRef = this.modalService.show(template, { animated: true });\n  }\n\n  addLanguage() {\n    this.addLangModalRef?.hide();\n    const language = this.allLanguages.find(o => o?.code === this.selectedLanguage);\n\n    const languagesArray = this.createForm.get('languages') as FormArray;\n    const isLanguageAlreadySelected = languagesArray.controls.some(control =>\n      control.get('languageCode')?.value === this.selectedLanguage\n    );\n\n    if (isLanguageAlreadySelected) {\n      this.toastr.error('The same language is already selected.', 'Error!');\n      return;\n    }\n\n    const langFormGroup = this.buildLanguageFormGroup(language);\n    languagesArray.push(langFormGroup);\n    this.updateLanguageValidation(this.createForm.get('channelType')?.value);\n    this.createForm.updateValueAndValidity();\n  }\n\n  removeLanguage(index: number) {\n    const languagesArray = this.createForm.get('languages') as FormArray;\n\n    if (languagesArray.length <= 1) {\n      return;\n    }\n\n    const removedLanguage = languagesArray.at(index).value;\n    languagesArray.removeAt(index);\n\n    if (this.fValue?.activeLanguage === removedLanguage?.languageCode) {\n      const firstLanguage = languagesArray.at(0)?.value;\n      if (firstLanguage) {\n        this.createForm.patchValue({ activeLanguage: firstLanguage.languageCode });\n      }\n    }\n\n    this.createForm.updateValueAndValidity();\n  }\n\n  createTemplate() {\n    // this.markFormGroupTouched(this.createForm);\n    console.log('Letter Header Content:', this.html);\n    console.log('Letter Footer Content:', this.html2);\n    console.log('Full Form Value:', this.createForm.value);\n\n    // if (this.createForm.invalid) {\n    //   this.toastr.error('Please fill all required fields.', 'Error');\n    //   return;\n    // }\n\n    // const formValue = this.createForm.value;\n    // const hasTemplateBody = formValue.languages?.some((lang: any) => lang.templateBody && lang.templateBody.trim());\n\n    // if (!hasTemplateBody) {\n    //   this.toastr.error('Please enter template body content.', 'Error');\n    //   return;\n    // }\n\n    // if (this.hasUnmappedVariables(formValue.languages)) {\n    //   this.toastr.error('Please map all variables and continue creating template.', 'Error');\n    //   return;\n    // }\n\n    // if (formValue.channelType === 'email') {\n    //   const hasEmailSubject = formValue.languages?.every((lang: any) => lang.emailSubject && lang.emailSubject.trim());\n    //   if (!hasEmailSubject) {\n    //     this.toastr.error('Please enter email subject for all languages.', 'Error');\n    //     return;\n    //   }\n    // }\n\n    // const getTemplateType = (channelType: string): string => {\n    //   switch (channelType.toLowerCase()) {\n    //     case 'email': return 'Email';\n    //     case 'sms': return 'SMS';\n    //     case 'letter': return 'Letter';\n    //     default: return 'Email';\n    //   }\n    // };\n\n    // const getLanguageName = (languageCode: string): string => {\n    //   const language = this.allLanguages.find(lang => lang.code === languageCode);\n    //   return language ? language.name : 'English';\n    // };\n\n    // const communicationTemplateDetails = formValue.languages.map((lang: any) => {\n    //   const detail: any = {\n    //     \"Language\": getLanguageName(lang.languageCode),\n    //     \"body\": lang.templateBody || \"\"\n    //   };\n\n    //   if (formValue.channelType === 'email') {\n    //     detail.Subject = lang.emailSubject || \"\";\n    //   }\n\n    //   return detail;\n    // });\n\n    // const json = {\n    //   \"templateType\": getTemplateType(formValue.channelType),\n    //   \"Name\": formValue.templateName,\n    //   \"isAvailableInAccountDetails\": formValue.allowAccessFromAccount === 'true',\n    //   \"CommunicationTemplateDetails\": communicationTemplateDetails\n    // };\n\n    // this.templateService.saveCommunicationTemplate(json).subscribe({\n    //   next: () => {\n    //     this.toastr.success(`The Template \"${json.Name}\" has been created successfully.`, \"Success!\");\n    //     this.router.navigate(['communication/search-communication-templates']);\n    //   },\n    //   error: (error) => {\n    //     this.toastr.error(error, \"Error!\");\n    //   }\n    // });\n  }\n\n  updateTemplate() {\n    this.markFormGroupTouched(this.createForm);\n\n    if (this.createForm.invalid) {\n      this.toastr.error('Please fill in all required fields.', 'Error');\n      return;\n    }\n\n    const formValue = this.createForm.getRawValue();\n    const hasTemplateBody = formValue.languages?.some((lang: any) => lang.templateBody && lang.templateBody.trim());\n\n    if (!hasTemplateBody) {\n      this.toastr.error('Please enter template body content.', 'Error');\n      return;\n    }\n\n    if (this.hasUnmappedVariables(formValue.languages)) {\n      this.toastr.error('Please map all variables and continue creating template.', 'Error');\n      return;\n    }\n\n    if (!formValue.channelType) {\n      this.toastr.error('Please select a channel type.', 'Error');\n      return;\n    }\n\n    if (formValue.channelType === 'email') {\n      const hasEmailSubject = formValue.languages?.every((lang: any) => lang.emailSubject && lang.emailSubject.trim());\n      if (!hasEmailSubject) {\n        this.toastr.error('Please enter email subject for all languages.', 'Error');\n        return;\n      }\n    }\n\n    const getTemplateType = (channelType: string): string => {\n      if (!channelType || typeof channelType !== 'string') {\n        return 'Email';\n      }\n      switch (channelType.toLowerCase()) {\n        case 'email': return 'Email';\n        case 'sms': return 'SMS';\n        case 'letter': return 'Letter';\n        default: return 'Email';\n      }\n    };\n\n    const getLanguageName = (languageCode: string): string => {\n      if (!languageCode) return 'English';\n      const language = this.allLanguages.find(lang => lang.code === languageCode);\n      return language ? language.name : 'English';\n    };\n\n    const communicationTemplateDetails = formValue.languages?.map((lang: any) => {\n      const detail: any = {\n        \"Language\": getLanguageName(lang.languageCode),\n        \"body\": lang.templateBody || \"\"\n      };\n\n      if (formValue.channelType === 'email') {\n        detail.Subject = lang.emailSubject || \"\";\n      }\n\n      return detail;\n    }) || [];\n\n    const json = {\n      \"id\": this.templateId,\n      \"templateType\": getTemplateType(formValue.channelType),\n      \"Name\": formValue.templateName || \"\",\n      \"IsAvailableInAccountDetails\": formValue.allowAccessFromAccount === 'true',\n      \"CommunicationTemplateDetails\": communicationTemplateDetails\n    };\n\n    this.templateService.updateCcmTemplate(json).subscribe({\n      next: () => {\n        this.toastr.success(`The Template \"${json.Name}\" has been updated successfully.`, \"Success!\");\n        this.router.navigate(['communication/search-communication-templates']);\n      },\n      error: (error) => {\n        this.toastr.error(error, \"Error!\");\n      }\n    });\n  }\n\n  private loadLanguageList() {\n    this.templateService.languageList().subscribe({\n      next: (response: any) => {\n        this.mapLanguageResponse(response);\n      },\n      error: (error) => {\n        this.toastr.error(error, 'Error');\n        this.allLanguages = [{ name: \"English\", code: \"en\" }];\n        this.initializeDefaultLanguage();\n      }\n    });\n  }\n\n  private mapLanguageResponse(apiResponse: any) {\n    if (!apiResponse || !Array.isArray(apiResponse)) {\n      this.allLanguages = [{ name: \"English\", code: \"en\" }];\n      this.initializeDefaultLanguage();\n      return;\n    }\n\n    this.allLanguages = apiResponse.map(lang => ({\n      name: lang.name || lang.itemName || 'Unknown Language',\n      code: lang.code || lang.itemCode || lang.name?.toLowerCase().substring(0, 2) || 'en'\n    }));\n\n    if (this.allLanguages.length === 0) {\n      this.allLanguages = [{ name: \"English\", code: \"en\" }];\n    }\n\n    this.initializeDefaultLanguage();\n  }\n\n  private initializeDefaultLanguage() {\n    if (this.createForm && this.allLanguages.length > 0) {\n      const currentActiveLanguage = this.createForm.get('activeLanguage')?.value;\n      if (!currentActiveLanguage || !this.allLanguages.find(lang => lang.code === currentActiveLanguage)) {\n        this.createForm.patchValue({ activeLanguage: this.allLanguages[0].code });\n\n        const languagesArray = this.createForm.get('languages') as FormArray;\n        if (languagesArray.length === 0) {\n          const defaultLanguageGroup = this.buildLanguageFormGroup(this.allLanguages[0]);\n          languagesArray.push(defaultLanguageGroup);\n        }\n      }\n    }\n  }\n\n  private markFormGroupTouched(formGroup: FormGroup | FormArray): void {\n    Object.keys(formGroup.controls).forEach(key => {\n      const control = formGroup.get(key);\n      if (control instanceof FormGroup || control instanceof FormArray) {\n        this.markFormGroupTouched(control);\n      } else {\n        control?.markAsTouched();\n      }\n    });\n  }\n\n  isFieldInvalid(fieldName: string): boolean {\n    const field = this.createForm.get(fieldName);\n    return !!(field && field.invalid && (field.dirty || field.touched));\n  }\n\n  getFieldErrorMessage(fieldName: string): string {\n    const field = this.createForm.get(fieldName);\n    if (field && field.errors && (field.dirty || field.touched)) {\n      if (field.errors['required']) {\n        return `${this.getFieldDisplayName(fieldName)} is required`;\n      }\n    }\n    return '';\n  }\n\n  isLanguageFieldInvalid(languageIndex: number, fieldName: string): boolean {\n    const languagesArray = this.createForm.get('languages') as FormArray;\n    const languageGroup = languagesArray.at(languageIndex) as FormGroup;\n    const field = languageGroup?.get(fieldName);\n    return !!(field && field.invalid && (field.dirty || field.touched));\n  }\n\n  getLanguageFieldErrorMessage(languageIndex: number, fieldName: string): string {\n    const languagesArray = this.createForm.get('languages') as FormArray;\n    const languageGroup = languagesArray.at(languageIndex) as FormGroup;\n    const field = languageGroup?.get(fieldName);\n    if (field && field.errors && (field.dirty || field.touched)) {\n      if (field.errors['required']) {\n        return `${this.getFieldDisplayName(fieldName)} is required`;\n      }\n    }\n    return '';\n  }\n\n  private hasUnmappedVariables(languages: any[]): boolean {\n    if (!languages || languages.length === 0) {\n      return false;\n    }\n\n    return languages.some(lang => {\n      const templateBody = lang.templateBody || '';\n      return templateBody.includes('<<Var>>');\n    });\n  }\n\n  private getFieldDisplayName(fieldName: string): string {\n    const fieldNames: { [key: string]: string } = {\n      'templateName': 'Template Name',\n      'channelType': 'Channel Type',\n      'emailSubject': 'Subject Line',\n      'templateBody': 'Template Body'\n    };\n    return fieldNames[fieldName] || fieldName;\n  }\n\n  insertVariable() {\n    const activeLanguageIndex = this.getActiveLanguageIndex();\n    if (activeLanguageIndex === -1) return;\n\n    const control = (this.createForm.get('languages') as FormArray).at(activeLanguageIndex).get('templateBody');\n    const currentValue = control.value || '';\n  \n    const textareaElement = document.getElementById('templateBody') as HTMLTextAreaElement;\n    if (!textareaElement) return;\n  \n    const cursorPosition = textareaElement.selectionStart;\n    \n   \n    const newValue = currentValue.substring(0, cursorPosition) + \n                    '<<Var>>' + \n                    currentValue.substring(textareaElement.selectionEnd || cursorPosition);\n  \n    control.setValue(newValue);\n    control.updateValueAndValidity();\n    setTimeout(() => {\n      textareaElement.focus();\n      textareaElement.setSelectionRange(cursorPosition + 7, cursorPosition + 7);\n    }, 0);\n  }\n\n  getActiveLanguageIndex(): number {\n    const activeLanguage = this.createForm.get('activeLanguage').value;\n    const languages = this.createForm.get('languages') as FormArray;\n\n    for (let i = 0; i < languages.length; i++) {\n      const lang = languages.at(i).get('languageCode').value;\n      if (lang === activeLanguage) {\n        return i;\n      }\n    }\n    return -1;\n  }\n\n  shouldRestrictHtmlTags(): boolean {\n    return this.createForm?.get('channelType')?.value !== 'sms';\n  }\n\n  // Method to convert HTML to Markdown when editor content changes\n  onEditorContentChange() {\n  const markdown = this.turndownService.turndown(this.html);\n  this.createForm.patchValue({\n    letterHeader: markdown\n  });\n}\n\n  // Method to convert specific HTML example to Markdown\n  convertExampleHtml() {\n  const exampleHtml = '<p><strong>Hello</strong>, this is <em>sample</em> content.</p>';\n  const markdownResult = this.turndownService.turndown(exampleHtml);\n\n  console.log('Example HTML:', exampleHtml);\n  console.log('Converted Markdown:', markdownResult);\n\n  this.html = exampleHtml;\n  this.createForm.patchValue({\n    letterHeader: markdownResult\n  });\n\n  return markdownResult;\n}\n\n\n  // Add this method to get markdown content\n  getMarkdownContent(htmlContent: string): string {\n    // Simple HTML to Markdown conversion for common elements\n    let markdown = htmlContent;\n  \n    // Replace paragraph tags\n    markdown = markdown.replace(/<p>(.*?)<\\/p>/g, '$1\\n\\n');\n  \n    // Replace strong/bold tags\n    markdown = markdown.replace(/<strong>(.*?)<\\/strong>/g, '**$1**');\n  \n    // Replace emphasis/italic tags\n    markdown = markdown.replace(/<em>(.*?)<\\/em>/g, '*$1*');\n  \n    // Replace links\n    markdown = markdown.replace(/<a href=\"(.*?)\">(.*?)<\\/a>/g, '[$2]($1)');\n  \n    // Replace headers\n    markdown = markdown.replace(/<h1>(.*?)<\\/h1>/g, '# $1\\n');\n    markdown = markdown.replace(/<h2>(.*?)<\\/h2>/g, '## $1\\n');\n    markdown = markdown.replace(/<h3>(.*?)<\\/h3>/g, '### $1\\n');\n  \n    // Replace line breaks\n    markdown = markdown.replace(/<br\\s*\\/?>/g, '\\n');\n  \n    return markdown;\n  }\n\n}\n"], "mappings": ";;;AAAA,SAASA,SAAS,EAAEC,MAAM,EAAeC,SAAS,EAAUC,gBAAgB,QAAQ,eAAe;AACnG,SACEC,SAAS,EACTC,WAAW,EACXC,SAAS,EACTC,WAAW,EACXC,mBAAmB,EACnBC,UAAU,QACL,gBAAgB;AACvB,SAASC,cAAc,EAAEC,MAAM,QAAQ,iBAAiB;AACxD,SAAqBC,cAAc,QAAQ,qBAAqB;AAChE,SAASC,aAAa,QAAQ,YAAY;AAC1C,SAASC,YAAY,QAAQ,gBAAgB;AAC7C,SAASC,0BAA0B,QAAQ,yDAAyD;AACpG,SAASC,wBAAwB,QAAQ,sDAAsD;AAC/F,SAASC,yBAAyB,QAAQ,wDAAwD;AAClG,SAASC,eAAe,QAAQ,qBAAqB;AACrD,SAASC,kBAAkB,EAAEC,sBAAsB,EAAEC,MAAM,QAAQ,YAAY;AAC/E,OAAOC,eAAe,MAAM,UAAU;AAmB/B,IAAMC,uBAAuB,GAA7B,MAAMA,uBAAuB;EAuClCC,YAAA;IAtCQ,KAAAC,EAAE,GAAgBxB,MAAM,CAACI,WAAW,CAAC;IACrC,KAAAqB,YAAY,GAAmBzB,MAAM,CAACW,cAAc,CAAC;IACrD,KAAAe,KAAK,GAAmB1B,MAAM,CAACS,cAAc,CAAC;IAC9C,KAAAkB,MAAM,GAAW3B,MAAM,CAACU,MAAM,CAAC;IAC/B,KAAAkB,eAAe,GAAoB5B,MAAM,CAACiB,eAAe,CAAC;IAC1D,KAAAY,MAAM,GAAkB7B,MAAM,CAACY,aAAa,CAAC;IAErD,KAAAkB,cAAc,GAAG,CACf;MAAEC,KAAK,EAAE;IAAe,CAAE,EAC1B;MAAEA,KAAK,EAAE;IAA+B,CAAE,CAC3C;IAED,KAAAC,UAAU,GAAY,KAAK;IAC3B,KAAAC,UAAU,GAAY,KAAK;IAC3B,KAAAC,UAAU,GAAkB,IAAI;IAChC,KAAAC,SAAS,GAAY,KAAK;IAC1B,KAAAC,YAAY,GAAQ,IAAI;IACxB,KAAAC,mBAAmB,GAAQ,IAAI;IAC/B,KAAAC,yBAAyB,GAAW,CAAC;IACrC,KAAAC,YAAY,GAAU,EAAE;IAExB,KAAAC,SAAS,GAAgD,EAAE;IAC3D,KAAAC,eAAe,GAAG,KAAK;IACvB,KAAAC,gBAAgB,GAAmC;MAAEC,KAAK,EAAE,IAAI;MAAEC,KAAK,EAAE,CAAC;IAAC,CAAE;IAC7E,KAAAC,gBAAgB,GAAW,IAAI;IAK/B,KAAAC,IAAI,GAAG,EAAE;IACT,KAAAC,KAAK,GAAG,EAAE;IACV,KAAAC,KAAK,GAAG,EAAE;IAKV,KAAAC,eAAe,GAAG,IAAI5B,eAAe,EAAE;IAGrC,IAAI,CAAC6B,uBAAuB,EAAE;IAC9B,IAAI,CAACC,MAAM,GAAG,IAAI/B,MAAM,EAAE;IAC1B,IAAI,CAACgC,OAAO,GAAG,IAAIhC,MAAM,EAAE;IAC3B,IAAI,CAACiC,OAAO,GAAG,IAAIjC,MAAM,EAAE;EAC7B;EAEAkC,QAAQA,CAAA;IACN;IACA,IAAI,CAACH,MAAM,GAAG,IAAI/B,MAAM,EAAE;IAC1B,IAAI,CAACgC,OAAO,GAAG,IAAIhC,MAAM,EAAE;IAC3B,IAAI,CAACiC,OAAO,GAAG,IAAIjC,MAAM,EAAE;IAE3B;IACA,IAAI,CAAC6B,eAAe,GAAG,IAAI5B,eAAe,CAAC;MACzCkC,YAAY,EAAE,KAAK;MACnBC,EAAE,EAAE,KAAK;MACTC,gBAAgB,EAAE,GAAG;MACrBC,cAAc,EAAE,QAAQ;MACxBC,WAAW,EAAE,GAAG;MAChBC,eAAe,EAAE;KAClB,CAAC;IAEF,IAAI,CAACC,kBAAkB,EAAE;IACzB,IAAI,CAACC,gBAAgB,EAAE;IACvB,IAAI,CAACpC,KAAK,CAACqC,MAAM,CAACC,SAAS,CAACD,MAAM,IAAG;MACnC,IAAI,CAAC7B,UAAU,GAAG6B,MAAM,CAAC,IAAI,CAAC,IAAI,IAAI;MAEtC,MAAME,GAAG,GAAG,IAAI,CAACtC,MAAM,CAACsC,GAAG;MAC3B,IAAIA,GAAG,CAACC,QAAQ,CAAC,6BAA6B,CAAC,EAAE;QAC/C,IAAI,CAAClC,UAAU,GAAG,IAAI;QACtB,IAAI,CAACC,UAAU,GAAG,KAAK;QACvB,IAAI,CAACkC,gBAAgB,CAAC,6BAA6B,CAAC;MACtD,CAAC,MAAM,IAAIF,GAAG,CAACC,QAAQ,CAAC,6BAA6B,CAAC,EAAE;QACtD,IAAI,CAAClC,UAAU,GAAG,KAAK;QACvB,IAAI,CAACC,UAAU,GAAG,IAAI;QACtB,IAAI,CAACkC,gBAAgB,CAAC,6BAA6B,CAAC;MACtD,CAAC,MAAM;QACL,IAAI,CAACnC,UAAU,GAAG,KAAK;QACvB,IAAI,CAACC,UAAU,GAAG,KAAK;QACvB,IAAI,CAACkC,gBAAgB,CAAC,+BAA+B,CAAC;MACxD;MAEA,IAAI,IAAI,CAACjC,UAAU,KAAK,IAAI,CAACF,UAAU,IAAI,IAAI,CAACC,UAAU,CAAC,EAAE;QAC3D,IAAI,CAACmC,gBAAgB,CAAC,IAAI,CAAClC,UAAU,CAAC;MACxC;IACF,CAAC,CAAC;IAEF;EAEF;EAEQiC,gBAAgBA,CAACpC,KAAa;IACpC,IAAI,CAACD,cAAc,GAAG,CACpB;MAAEC,KAAK,EAAE;IAAe,CAAE,EAC1B;MAAEA,KAAK,EAAEA;IAAK,CAAE,CACjB;EACH;EAEQqC,gBAAgBA,CAAClC,UAAkB;IACzC,IAAI,CAACC,SAAS,GAAG,IAAI;IAErB,IAAI,IAAI,CAACI,YAAY,CAAC8B,MAAM,KAAK,CAAC,EAAE;MAClC,IAAI,CAACzC,eAAe,CAAC0C,YAAY,EAAE,CAACN,SAAS,CAAC;QAC5CO,IAAI,EAAGC,gBAAqB,IAAI;UAC9B,IAAI,CAACC,mBAAmB,CAACD,gBAAgB,CAAC;UAC1C,IAAI,CAACE,wBAAwB,CAACxC,UAAU,CAAC;QAC3C,CAAC;QACDyC,KAAK,EAAEA,CAAA,KAAK;UACV,IAAI,CAACpC,YAAY,GAAG,CAAC;YAAEqC,IAAI,EAAE,SAAS;YAAEC,IAAI,EAAE;UAAI,CAAE,CAAC;UACrD,IAAI,CAACH,wBAAwB,CAACxC,UAAU,CAAC;QAC3C;OACD,CAAC;IACJ,CAAC,MAAM;MACL,IAAI,CAACwC,wBAAwB,CAACxC,UAAU,CAAC;IAC3C;EACF;EAEQwC,wBAAwBA,CAACxC,UAAkB;IACjD,IAAI,CAACN,eAAe,CAACkD,iBAAiB,CAAC5C,UAAU,CAAC,CAAC8B,SAAS,CAAC;MAC3DO,IAAI,EAAGQ,QAAa,IAAI;QACtB,IAAI,CAAC3C,YAAY,GAAG2C,QAAQ;QAC5B,IAAI,CAACC,4BAA4B,CAACD,QAAQ,CAAC;QAC3C,IAAI,CAAC5C,SAAS,GAAG,KAAK;MACxB,CAAC;MACDwC,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAACxC,SAAS,GAAG,KAAK;QACtB,IAAI,CAACN,MAAM,CAAC8C,KAAK,CAACA,KAAK,EAAE,QAAQ,CAAC;MACpC;KACD,CAAC;EACJ;EAEA,IAAIM,KAAKA,CAAA;IACT,OAAO,IAAI,CAACC,UAAU,EAAEC,GAAG,CAAC,aAAa,CAAC,EAAExC,KAAK,EAAEyC,WAAW,EAAE,KAAK,KAAK;EAC5E;EAGUJ,4BAA4BA,CAACK,WAAgB;IACnD,IAAI,CAACA,WAAW,EAAE;IAElB,MAAMjD,YAAY,GAAGiD,WAAW,CAACC,IAAI,IAAID,WAAW;IACpD,MAAME,eAAe,GAAGnD,YAAY,CAACoD,4BAA4B,IAAI,EAAE;IACvE,IAAI,CAACnD,mBAAmB,GAAGD,YAAY;IAEvC;IACA,MAAMqD,gBAAgB,GAAGrD,YAAY,CAACsD,2BAA2B,KAAK,IAAI,GAAG,MAAM,GAAG,OAAO;IAE7F,IAAI,CAACR,UAAU,CAACS,UAAU,CAAC;MACzBC,WAAW,EAAExD,YAAY,CAACyD,YAAY,EAAET,WAAW,EAAE,IAAI,OAAO;MAChEU,YAAY,EAAE1D,YAAY,CAACwC,IAAI,IAAI,EAAE;MACrCmB,sBAAsB,EAAEN;KACzB,CAAC;IAEF,MAAMO,cAAc,GAAG,IAAI,CAACd,UAAU,CAACC,GAAG,CAAC,WAAW,CAAc;IACpE,OAAOa,cAAc,CAAC3B,MAAM,KAAK,CAAC,EAAE;MAClC2B,cAAc,CAACC,QAAQ,CAAC,CAAC,CAAC;IAC5B;IAEA,IAAIV,eAAe,CAAClB,MAAM,GAAG,CAAC,EAAE;MAC9BkB,eAAe,CAACW,OAAO,CAAEC,MAAW,IAAI;QACtC,IAAIC,YAAY,GAAG,IAAI;QACvB,MAAMC,YAAY,GAAGF,MAAM,CAACG,QAAQ,IAAI,SAAS;QAEjD,IAAI,IAAI,CAAC/D,YAAY,IAAI,IAAI,CAACA,YAAY,CAAC8B,MAAM,GAAG,CAAC,EAAE;UACrD,MAAMkC,aAAa,GAAG,IAAI,CAAChE,YAAY,CAACiE,IAAI,CAACC,IAAI,IAC/CA,IAAI,CAAC7B,IAAI,CAACQ,WAAW,EAAE,KAAKiB,YAAY,CAACjB,WAAW,EAAE,CACvD;UACD,IAAImB,aAAa,EAAE;YACjBH,YAAY,GAAGG,aAAa,CAAC1B,IAAI;UACnC;QACF;QAEA,MAAM6B,iBAAiB,GAAG,IAAI,CAACC,sBAAsB,CAAC;UACpD9B,IAAI,EAAEuB,YAAY;UAClBxB,IAAI,EAAEyB;SACP,CAAC;QAEFK,iBAAiB,CAACf,UAAU,CAAC;UAC3BS,YAAY,EAAEA,YAAY;UAC1BC,YAAY,EAAEA,YAAY;UAC1BO,YAAY,EAAET,MAAM,CAACU,OAAO,IAAI,EAAE;UAClCC,YAAY,EAAEX,MAAM,CAACY,IAAI,IAAI;SAC9B,CAAC;QACFf,cAAc,CAACgB,IAAI,CAACN,iBAAiB,CAAC;MACxC,CAAC,CAAC;IACJ,CAAC,MAAM;MACL,MAAMO,oBAAoB,GAAG,IAAI,CAACN,sBAAsB,CAAC;QACvD9B,IAAI,EAAE,IAAI;QACVD,IAAI,EAAE;OACP,CAAC;MACFoB,cAAc,CAACgB,IAAI,CAACC,oBAAoB,CAAC;IAC3C;IAEA,IAAIjB,cAAc,CAAC3B,MAAM,GAAG,CAAC,EAAE;MAC7B,MAAM6C,aAAa,GAAGlB,cAAc,CAACmB,EAAE,CAAC,CAAC,CAAC,EAAExE,KAAK;MACjD,MAAMyE,cAAc,GAAGF,aAAa,EAAEd,YAAY,IAAI,IAAI;MAC1D,IAAI,CAAClB,UAAU,CAACS,UAAU,CAAC;QAAEyB;MAAc,CAAE,CAAC;IAChD;IAEAC,UAAU,CAAC,MAAK;MACd,IAAI,IAAI,CAACrF,UAAU,EAAE;QACnB,IAAI,CAACkD,UAAU,CAACoC,OAAO,EAAE;MAC3B,CAAC,MAAM,IAAI,IAAI,CAACrF,UAAU,EAAE;QAC1B,IAAI,CAACiD,UAAU,CAACC,GAAG,CAAC,aAAa,CAAC,EAAEmC,OAAO,EAAE;QAC7C,IAAI,CAACpC,UAAU,CAACC,GAAG,CAAC,cAAc,CAAC,EAAEmC,OAAO,EAAE;MAChD;IACF,CAAC,EAAE,GAAG,CAAC;IAEP,IAAI,IAAI,CAACtF,UAAU,EAAE;MACnB,IAAI,CAACmC,gBAAgB,CAAC,6BAA6B,CAAC;MACpD,IAAI,CAAC7B,yBAAyB,GAAG,CAAC;IACpC,CAAC,MAAM,IAAI,IAAI,CAACL,UAAU,EAAE;MAC1B,IAAI,CAACkC,gBAAgB,CAAC,6BAA6B,CAAC;IACtD;EACF;EAGQN,kBAAkBA,CAAA;IACxB,IAAI,CAACpB,eAAe,GAAG,IAAI;IAE3B,IAAI,CAACb,eAAe,CAAC2F,aAAa,EAAE,CAACvD,SAAS,CAAC;MAC7CO,IAAI,EAAGQ,QAAa,IAAI;QACtB,IAAI,CAACyC,oBAAoB,CAACzC,QAAQ,CAAC;QACnC,IAAI,CAACtC,eAAe,GAAG,KAAK;MAC9B,CAAC;MACDkC,KAAK,EAAEA,CAAA,KAAK;QACV,IAAI,CAAClC,eAAe,GAAG,KAAK;QAC5B,IAAI,CAACZ,MAAM,CAAC8C,KAAK,CAAC,OAAO,EAAE,QAAQ,CAAC;QACpC,IAAI,CAACnC,SAAS,GAAG,EAAE;MACrB;KACD,CAAC;EACJ;EAEQgF,oBAAoBA,CAACnC,WAAkB;IAC7C,IAAI,CAACoC,KAAK,CAACC,OAAO,CAACrC,WAAW,CAAC,EAAE;MAC/B,IAAI,CAAC7C,SAAS,GAAG,EAAE;MACnB;IACF;IAEA,IAAI,CAACA,SAAS,GAAG6C,WAAW,CAACsC,GAAG,CAACC,KAAK,KAAK;MACzChD,IAAI,EAAEgD,KAAK,CAAChD,IAAI,IAAIgD,KAAK,CAACC,SAAS,IAAI,eAAe;MACtDC,EAAE,EAAEF,KAAK,CAACE,EAAE,IAAIF,KAAK,CAAC/C,IAAI,IAAI+C,KAAK,CAAChD,IAAI,EAAEmD,WAAW,EAAE,CAACC,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,IAAI,SAAS;MACzFnD,IAAI,EAAE+C,KAAK,CAAC/C,IAAI,IAAI+C,KAAK,CAACE,EAAE,IAAI;KACjC,CAAC,CAAC;EACL;EAEA5E,uBAAuBA,CAAA;IACrB,IAAI,CAACgC,UAAU,GAAG,IAAI,CAAC1D,EAAE,CAACyG,KAAK,CAAC;MAC9BrC,WAAW,EAAE,CAAC,OAAO,EAAE,CAACpF,UAAU,CAAC0H,QAAQ,CAAC,CAAC;MAC7CpC,YAAY,EAAE,CAAC,IAAI,EAAE,CAACtF,UAAU,CAAC0H,QAAQ,CAAC,CAAC;MAC3CnC,sBAAsB,EAAE,CAAC,MAAM,CAAC;MAChCqB,cAAc,EAAE,CAAC,IAAI,CAAC;MACtBe,YAAY,EAAE,CAAC,EAAE,CAAC;MAAE;MACpBC,YAAY,EAAE,CAAC,EAAE,CAAC;MAClBC,SAAS,EAAE,IAAI,CAAC7G,EAAE,CAAC8G,KAAK,CAAC,EAAE;KAE5B,CAAC;IAEF,IAAI,CAACpD,UAAU,CAACC,GAAG,CAAC,aAAa,CAAC,EAAEoD,YAAY,CAACvE,SAAS,CAAC4B,WAAW,IAAG;MACvE,IAAI,CAAC4C,wBAAwB,CAAC5C,WAAW,CAAC;IAC5C,CAAC,CAAC;IAEF,IAAI,CAAC4C,wBAAwB,CAAC,IAAI,CAACtD,UAAU,CAACC,GAAG,CAAC,aAAa,CAAC,EAAExC,KAAK,CAAC;EAC1E;EAEQ6F,wBAAwBA,CAAC5C,WAAmB;IAClD,MAAMI,cAAc,GAAG,IAAI,CAACd,UAAU,CAACC,GAAG,CAAC,WAAW,CAAc;IAEpEa,cAAc,CAACyC,QAAQ,CAACvC,OAAO,CAACwC,eAAe,IAAG;MAChD,MAAMC,mBAAmB,GAAGD,eAAe,CAACvD,GAAG,CAAC,cAAc,CAAC;MAE/D,IAAIS,WAAW,KAAK,OAAO,EAAE;QAC3B+C,mBAAmB,EAAEC,aAAa,CAAC,CAACpI,UAAU,CAAC0H,QAAQ,CAAC,CAAC;MAC3D,CAAC,MAAM;QACLS,mBAAmB,EAAEE,eAAe,EAAE;MACxC;MAEAF,mBAAmB,EAAEG,sBAAsB,EAAE;IAC/C,CAAC,CAAC;EACJ;EAEAC,uBAAuBA,CAACzD,IAAY;IAClC,MAAM0D,SAAS,GAAG,IAAI7I,SAAS,CAAC,EAAE,CAAC;IAEnC,IAAImF,IAAI,IAAIA,IAAI,CAACjB,MAAM,GAAG,CAAC,EAAE;MAC3BiB,IAAI,CAACY,OAAO,CAAE+C,CAAC,IAAI;QACjBD,SAAS,CAAChC,IAAI,CAAC,IAAI,CAACL,sBAAsB,CAACsC,CAAC,CAAC,CAAC;MAChD,CAAC,CAAC;IACJ,CAAC,MAAM,IAAI,IAAI,CAAC1G,YAAY,IAAI,IAAI,CAACA,YAAY,CAAC8B,MAAM,GAAG,CAAC,EAAE;MAC5D2E,SAAS,CAAChC,IAAI,CAAC,IAAI,CAACL,sBAAsB,CAAC,IAAI,CAACpE,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC;IACnE;IAEA,OAAOyG,SAAS;EAClB;EAEArC,sBAAsBA,CAACrB,IAAU;IAC/B,OAAO,IAAI,CAAC9D,EAAE,CAACyG,KAAK,CAAC;MACnB7B,YAAY,EAAEd,IAAI,EAAET,IAAI;MACxBwB,YAAY,EAAEf,IAAI,EAAEV,IAAI;MACxBgC,YAAY,EAAE,CAAC,IAAI,CAAC;MACpBE,YAAY,EAAE,CAAC,IAAI,EAAE,CAACtG,UAAU,CAAC0H,QAAQ,CAAC;KAC3C,CAAC;EACJ;EAEA,IAAIgB,MAAMA,CAAA;IACR,OAAO,IAAI,CAAChE,UAAU,CAACvC,KAAK;EAC9B;EAEAwG,wBAAwBA,CAACC,aAAqB;IAC5C,IAAI,IAAI,CAACpH,UAAU,EAAE;MACnB,OAAOoH,aAAa,KAAK,CAAC;IAC5B;IACA,MAAMhC,cAAc,GAAG,IAAI,CAAC8B,MAAM,EAAE9B,cAAc;IAClD,MAAMiC,eAAe,GAAG,IAAI,CAACH,MAAM,EAAEb,SAAS,GAAGe,aAAa,CAAC;IAC/D,OAAOhC,cAAc,KAAKiC,eAAe,EAAEjD,YAAY;EACzD;EAEAkD,kBAAkBA,CAAC1G,KAAa;IAC9B,IAAI,CAACN,yBAAyB,GAAGM,KAAK;EACxC;EAEA2G,yBAAyBA,CAAA;IACvB,IAAI,IAAI,CAACvH,UAAU,IAAI,IAAI,CAACK,mBAAmB,EAAEmD,4BAA4B,EAAEnB,MAAM,GAAG,CAAC,EAAE;MACzF,OAAO,IAAI,CAAChC,mBAAmB,CAACmD,4BAA4B,CAAC,IAAI,CAAClD,yBAAyB,CAAC,IACrF,IAAI,CAACD,mBAAmB,CAACmD,4BAA4B,CAAC,CAAC,CAAC;IACjE;IACA,OAAO,IAAI;EACb;EAEAgE,uBAAuBA,CAAA;IACrB,IAAIC,UAAU,GAAQ,EAAE;IAExBC,MAAM,CAACC,IAAI,CAAC,IAAI,CAACzE,UAAU,CAACuD,QAAQ,CAAC,CAACvC,OAAO,CAAC0D,GAAG,IAAG;MAClD,MAAMC,aAAa,GAAG,IAAI,CAAC3E,UAAU,CAACC,GAAG,CAACyE,GAAG,CAAC,EAAEE,MAAM;MACtD,IAAID,aAAa,EAAE;QACjBJ,UAAU,CAACG,GAAG,CAAC,GAAGC,aAAa;MACjC;IACF,CAAC,CAAC;IAEF,MAAM7D,cAAc,GAAG,IAAI,CAACd,UAAU,CAACC,GAAG,CAAC,WAAW,CAAc;IACpE,IAAIa,cAAc,EAAE;MAClBA,cAAc,CAACyC,QAAQ,CAACvC,OAAO,CAAC,CAAC6D,OAAO,EAAEnH,KAAK,KAAI;QACjD,MAAMoH,SAAS,GAAGD,OAAoB;QACtCL,MAAM,CAACC,IAAI,CAACK,SAAS,CAACvB,QAAQ,CAAC,CAACvC,OAAO,CAAC+D,QAAQ,IAAG;UACjD,MAAMC,YAAY,GAAGF,SAAS,CAAC7E,GAAG,CAAC8E,QAAQ,CAAC;UAC5C,IAAIC,YAAY,EAAEJ,MAAM,EAAE;YACxB,IAAI,CAACL,UAAU,CAACpB,SAAS,EAAEoB,UAAU,CAACpB,SAAS,GAAG,EAAE;YACpD,IAAI,CAACoB,UAAU,CAACpB,SAAS,CAACzF,KAAK,CAAC,EAAE6G,UAAU,CAACpB,SAAS,CAACzF,KAAK,CAAC,GAAG,EAAE;YAClE6G,UAAU,CAACpB,SAAS,CAACzF,KAAK,CAAC,CAACqH,QAAQ,CAAC,GAAGC,YAAY,CAACJ,MAAM;UAC7D;QACF,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ;IAEA,OAAOL,UAAU;EACnB;EAEAU,oBAAoBA,CAACC,KAAqC,EAAEC,QAA0B;IACpF,IAAI,CAAC3H,gBAAgB,GAAG0H,KAAK;IAC7B,IAAI,CAACE,cAAc,GAAG,IAAI,CAAC7I,YAAY,CAAC8I,IAAI,CAACF,QAAQ,EAAE;MACrDG,QAAQ,EAAE;KACX,CAAC;EACJ;EAEAC,cAAcA,CAAA;IACZ,IAAI,CAACH,cAAc,CAACI,IAAI,EAAE;IAC1B,IAAI,IAAI,CAAChI,gBAAgB,EAAEC,KAAK,EAAE;MAChC,IAAI,CAACgI,iBAAiB,CAACC,gBAAgB,CAAC,IAAI,CAAClI,gBAAgB,CAAC;MAC9D,IAAI,CAACA,gBAAgB,GAAG;QAAEC,KAAK,EAAE,IAAI;QAAEC,KAAK,EAAE,CAAC;MAAC,CAAE;IACpD;EACF;EAEAiI,mBAAmBA,CAACR,QAAgB,EAAEzH,KAAa;IACjD,MAAM8F,eAAe,GAAI,IAAI,CAACxD,UAAU,CAACC,GAAG,CAAC,WAAW,CAAe,CAACgC,EAAE,CAACvE,KAAK,CAAC;IACjF8F,eAAe,CAAC/C,UAAU,CAAC;MAAEmB,YAAY,EAAEuD;IAAQ,CAAE,CAAC;IACtD3B,eAAe,CAACvD,GAAG,CAAC,cAAc,CAAC,EAAE2D,sBAAsB,EAAE;IAC7D,IAAI,CAAC5D,UAAU,CAAC4D,sBAAsB,EAAE;EAC1C;EAEAgC,gBAAgBA,CAACT,QAA0B;IACzC,IAAI,CAACU,eAAe,GAAG,IAAI,CAACtJ,YAAY,CAAC8I,IAAI,CAACF,QAAQ,EAAE;MAAEG,QAAQ,EAAE;IAAI,CAAE,CAAC;EAC7E;EAEAQ,WAAWA,CAAA;IACT,IAAI,CAACD,eAAe,EAAEL,IAAI,EAAE;IAC5B,MAAMpE,QAAQ,GAAG,IAAI,CAAC/D,YAAY,CAACiE,IAAI,CAACyC,CAAC,IAAIA,CAAC,EAAEpE,IAAI,KAAK,IAAI,CAAChC,gBAAgB,CAAC;IAE/E,MAAMmD,cAAc,GAAG,IAAI,CAACd,UAAU,CAACC,GAAG,CAAC,WAAW,CAAc;IACpE,MAAM8F,yBAAyB,GAAGjF,cAAc,CAACyC,QAAQ,CAACyC,IAAI,CAACnB,OAAO,IACpEA,OAAO,CAAC5E,GAAG,CAAC,cAAc,CAAC,EAAExC,KAAK,KAAK,IAAI,CAACE,gBAAgB,CAC7D;IAED,IAAIoI,yBAAyB,EAAE;MAC7B,IAAI,CAACpJ,MAAM,CAAC8C,KAAK,CAAC,wCAAwC,EAAE,QAAQ,CAAC;MACrE;IACF;IAEA,MAAMwG,aAAa,GAAG,IAAI,CAACxE,sBAAsB,CAACL,QAAQ,CAAC;IAC3DN,cAAc,CAACgB,IAAI,CAACmE,aAAa,CAAC;IAClC,IAAI,CAAC3C,wBAAwB,CAAC,IAAI,CAACtD,UAAU,CAACC,GAAG,CAAC,aAAa,CAAC,EAAExC,KAAK,CAAC;IACxE,IAAI,CAACuC,UAAU,CAAC4D,sBAAsB,EAAE;EAC1C;EAEAsC,cAAcA,CAACxI,KAAa;IAC1B,MAAMoD,cAAc,GAAG,IAAI,CAACd,UAAU,CAACC,GAAG,CAAC,WAAW,CAAc;IAEpE,IAAIa,cAAc,CAAC3B,MAAM,IAAI,CAAC,EAAE;MAC9B;IACF;IAEA,MAAMgH,eAAe,GAAGrF,cAAc,CAACmB,EAAE,CAACvE,KAAK,CAAC,CAACD,KAAK;IACtDqD,cAAc,CAACC,QAAQ,CAACrD,KAAK,CAAC;IAE9B,IAAI,IAAI,CAACsG,MAAM,EAAE9B,cAAc,KAAKiE,eAAe,EAAEjF,YAAY,EAAE;MACjE,MAAMc,aAAa,GAAGlB,cAAc,CAACmB,EAAE,CAAC,CAAC,CAAC,EAAExE,KAAK;MACjD,IAAIuE,aAAa,EAAE;QACjB,IAAI,CAAChC,UAAU,CAACS,UAAU,CAAC;UAAEyB,cAAc,EAAEF,aAAa,CAACd;QAAY,CAAE,CAAC;MAC5E;IACF;IAEA,IAAI,CAAClB,UAAU,CAAC4D,sBAAsB,EAAE;EAC1C;EAEAwC,cAAcA,CAAA;IACZ;IACAC,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAE,IAAI,CAAC1I,IAAI,CAAC;IAChDyI,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAE,IAAI,CAACzI,KAAK,CAAC;IACjDwI,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAE,IAAI,CAACtG,UAAU,CAACvC,KAAK,CAAC;IAEtD;IACA;IACA;IACA;IAEA;IACA;IAEA;IACA;IACA;IACA;IAEA;IACA;IACA;IACA;IAEA;IACA;IACA;IACA;IACA;IACA;IACA;IAEA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IAEA;IACA;IACA;IACA;IAEA;IACA;IACA;IACA;IACA;IAEA;IACA;IACA;IAEA;IACA;IAEA;IACA;IACA;IACA;IACA;IACA;IAEA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;EACF;EAEA8I,cAAcA,CAAA;IACZ,IAAI,CAACC,oBAAoB,CAAC,IAAI,CAACxG,UAAU,CAAC;IAE1C,IAAI,IAAI,CAACA,UAAU,CAACyG,OAAO,EAAE;MAC3B,IAAI,CAAC9J,MAAM,CAAC8C,KAAK,CAAC,qCAAqC,EAAE,OAAO,CAAC;MACjE;IACF;IAEA,MAAMiH,SAAS,GAAG,IAAI,CAAC1G,UAAU,CAAC2G,WAAW,EAAE;IAC/C,MAAMC,eAAe,GAAGF,SAAS,CAACvD,SAAS,EAAE6C,IAAI,CAAEzE,IAAS,IAAKA,IAAI,CAACK,YAAY,IAAIL,IAAI,CAACK,YAAY,CAACiF,IAAI,EAAE,CAAC;IAE/G,IAAI,CAACD,eAAe,EAAE;MACpB,IAAI,CAACjK,MAAM,CAAC8C,KAAK,CAAC,qCAAqC,EAAE,OAAO,CAAC;MACjE;IACF;IAEA,IAAI,IAAI,CAACqH,oBAAoB,CAACJ,SAAS,CAACvD,SAAS,CAAC,EAAE;MAClD,IAAI,CAACxG,MAAM,CAAC8C,KAAK,CAAC,0DAA0D,EAAE,OAAO,CAAC;MACtF;IACF;IAEA,IAAI,CAACiH,SAAS,CAAChG,WAAW,EAAE;MAC1B,IAAI,CAAC/D,MAAM,CAAC8C,KAAK,CAAC,+BAA+B,EAAE,OAAO,CAAC;MAC3D;IACF;IAEA,IAAIiH,SAAS,CAAChG,WAAW,KAAK,OAAO,EAAE;MACrC,MAAMqG,eAAe,GAAGL,SAAS,CAACvD,SAAS,EAAE6D,KAAK,CAAEzF,IAAS,IAAKA,IAAI,CAACG,YAAY,IAAIH,IAAI,CAACG,YAAY,CAACmF,IAAI,EAAE,CAAC;MAChH,IAAI,CAACE,eAAe,EAAE;QACpB,IAAI,CAACpK,MAAM,CAAC8C,KAAK,CAAC,+CAA+C,EAAE,OAAO,CAAC;QAC3E;MACF;IACF;IAEA,MAAMwH,eAAe,GAAIvG,WAAmB,IAAY;MACtD,IAAI,CAACA,WAAW,IAAI,OAAOA,WAAW,KAAK,QAAQ,EAAE;QACnD,OAAO,OAAO;MAChB;MACA,QAAQA,WAAW,CAACR,WAAW,EAAE;QAC/B,KAAK,OAAO;UAAE,OAAO,OAAO;QAC5B,KAAK,KAAK;UAAE,OAAO,KAAK;QACxB,KAAK,QAAQ;UAAE,OAAO,QAAQ;QAC9B;UAAS,OAAO,OAAO;MACzB;IACF,CAAC;IAED,MAAMgH,eAAe,GAAIhG,YAAoB,IAAY;MACvD,IAAI,CAACA,YAAY,EAAE,OAAO,SAAS;MACnC,MAAME,QAAQ,GAAG,IAAI,CAAC/D,YAAY,CAACiE,IAAI,CAACC,IAAI,IAAIA,IAAI,CAAC5B,IAAI,KAAKuB,YAAY,CAAC;MAC3E,OAAOE,QAAQ,GAAGA,QAAQ,CAAC1B,IAAI,GAAG,SAAS;IAC7C,CAAC;IAED,MAAMY,4BAA4B,GAAGoG,SAAS,CAACvD,SAAS,EAAEV,GAAG,CAAElB,IAAS,IAAI;MAC1E,MAAMN,MAAM,GAAQ;QAClB,UAAU,EAAEiG,eAAe,CAAC3F,IAAI,CAACL,YAAY,CAAC;QAC9C,MAAM,EAAEK,IAAI,CAACK,YAAY,IAAI;OAC9B;MAED,IAAI8E,SAAS,CAAChG,WAAW,KAAK,OAAO,EAAE;QACrCO,MAAM,CAACkG,OAAO,GAAG5F,IAAI,CAACG,YAAY,IAAI,EAAE;MAC1C;MAEA,OAAOT,MAAM;IACf,CAAC,CAAC,IAAI,EAAE;IAER,MAAMmG,IAAI,GAAG;MACX,IAAI,EAAE,IAAI,CAACpK,UAAU;MACrB,cAAc,EAAEiK,eAAe,CAACP,SAAS,CAAChG,WAAW,CAAC;MACtD,MAAM,EAAEgG,SAAS,CAAC9F,YAAY,IAAI,EAAE;MACpC,6BAA6B,EAAE8F,SAAS,CAAC7F,sBAAsB,KAAK,MAAM;MAC1E,8BAA8B,EAAEP;KACjC;IAED,IAAI,CAAC5D,eAAe,CAAC2K,iBAAiB,CAACD,IAAI,CAAC,CAACtI,SAAS,CAAC;MACrDO,IAAI,EAAEA,CAAA,KAAK;QACT,IAAI,CAAC1C,MAAM,CAAC2K,OAAO,CAAC,iBAAiBF,IAAI,CAACG,IAAI,kCAAkC,EAAE,UAAU,CAAC;QAC7F,IAAI,CAAC9K,MAAM,CAAC+K,QAAQ,CAAC,CAAC,8CAA8C,CAAC,CAAC;MACxE,CAAC;MACD/H,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAAC9C,MAAM,CAAC8C,KAAK,CAACA,KAAK,EAAE,QAAQ,CAAC;MACpC;KACD,CAAC;EACJ;EAEQb,gBAAgBA,CAAA;IACtB,IAAI,CAAClC,eAAe,CAAC0C,YAAY,EAAE,CAACN,SAAS,CAAC;MAC5CO,IAAI,EAAGQ,QAAa,IAAI;QACtB,IAAI,CAACN,mBAAmB,CAACM,QAAQ,CAAC;MACpC,CAAC;MACDJ,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAAC9C,MAAM,CAAC8C,KAAK,CAACA,KAAK,EAAE,OAAO,CAAC;QACjC,IAAI,CAACpC,YAAY,GAAG,CAAC;UAAEqC,IAAI,EAAE,SAAS;UAAEC,IAAI,EAAE;QAAI,CAAE,CAAC;QACrD,IAAI,CAAC8H,yBAAyB,EAAE;MAClC;KACD,CAAC;EACJ;EAEQlI,mBAAmBA,CAACY,WAAgB;IAC1C,IAAI,CAACA,WAAW,IAAI,CAACoC,KAAK,CAACC,OAAO,CAACrC,WAAW,CAAC,EAAE;MAC/C,IAAI,CAAC9C,YAAY,GAAG,CAAC;QAAEqC,IAAI,EAAE,SAAS;QAAEC,IAAI,EAAE;MAAI,CAAE,CAAC;MACrD,IAAI,CAAC8H,yBAAyB,EAAE;MAChC;IACF;IAEA,IAAI,CAACpK,YAAY,GAAG8C,WAAW,CAACsC,GAAG,CAAClB,IAAI,KAAK;MAC3C7B,IAAI,EAAE6B,IAAI,CAAC7B,IAAI,IAAI6B,IAAI,CAACmG,QAAQ,IAAI,kBAAkB;MACtD/H,IAAI,EAAE4B,IAAI,CAAC5B,IAAI,IAAI4B,IAAI,CAACoG,QAAQ,IAAIpG,IAAI,CAAC7B,IAAI,EAAEQ,WAAW,EAAE,CAAC0H,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI;KACjF,CAAC,CAAC;IAEH,IAAI,IAAI,CAACvK,YAAY,CAAC8B,MAAM,KAAK,CAAC,EAAE;MAClC,IAAI,CAAC9B,YAAY,GAAG,CAAC;QAAEqC,IAAI,EAAE,SAAS;QAAEC,IAAI,EAAE;MAAI,CAAE,CAAC;IACvD;IAEA,IAAI,CAAC8H,yBAAyB,EAAE;EAClC;EAEQA,yBAAyBA,CAAA;IAC/B,IAAI,IAAI,CAACzH,UAAU,IAAI,IAAI,CAAC3C,YAAY,CAAC8B,MAAM,GAAG,CAAC,EAAE;MACnD,MAAM0I,qBAAqB,GAAG,IAAI,CAAC7H,UAAU,CAACC,GAAG,CAAC,gBAAgB,CAAC,EAAExC,KAAK;MAC1E,IAAI,CAACoK,qBAAqB,IAAI,CAAC,IAAI,CAACxK,YAAY,CAACiE,IAAI,CAACC,IAAI,IAAIA,IAAI,CAAC5B,IAAI,KAAKkI,qBAAqB,CAAC,EAAE;QAClG,IAAI,CAAC7H,UAAU,CAACS,UAAU,CAAC;UAAEyB,cAAc,EAAE,IAAI,CAAC7E,YAAY,CAAC,CAAC,CAAC,CAACsC;QAAI,CAAE,CAAC;QAEzE,MAAMmB,cAAc,GAAG,IAAI,CAACd,UAAU,CAACC,GAAG,CAAC,WAAW,CAAc;QACpE,IAAIa,cAAc,CAAC3B,MAAM,KAAK,CAAC,EAAE;UAC/B,MAAM4C,oBAAoB,GAAG,IAAI,CAACN,sBAAsB,CAAC,IAAI,CAACpE,YAAY,CAAC,CAAC,CAAC,CAAC;UAC9EyD,cAAc,CAACgB,IAAI,CAACC,oBAAoB,CAAC;QAC3C;MACF;IACF;EACF;EAEQyE,oBAAoBA,CAAC1B,SAAgC;IAC3DN,MAAM,CAACC,IAAI,CAACK,SAAS,CAACvB,QAAQ,CAAC,CAACvC,OAAO,CAAC0D,GAAG,IAAG;MAC5C,MAAMG,OAAO,GAAGC,SAAS,CAAC7E,GAAG,CAACyE,GAAG,CAAC;MAClC,IAAIG,OAAO,YAAY1J,SAAS,IAAI0J,OAAO,YAAY5J,SAAS,EAAE;QAChE,IAAI,CAACuL,oBAAoB,CAAC3B,OAAO,CAAC;MACpC,CAAC,MAAM;QACLA,OAAO,EAAEiD,aAAa,EAAE;MAC1B;IACF,CAAC,CAAC;EACJ;EAEAC,cAAcA,CAACpF,SAAiB;IAC9B,MAAMD,KAAK,GAAG,IAAI,CAAC1C,UAAU,CAACC,GAAG,CAAC0C,SAAS,CAAC;IAC5C,OAAO,CAAC,EAAED,KAAK,IAAIA,KAAK,CAAC+D,OAAO,KAAK/D,KAAK,CAACsF,KAAK,IAAItF,KAAK,CAACuF,OAAO,CAAC,CAAC;EACrE;EAEAC,oBAAoBA,CAACvF,SAAiB;IACpC,MAAMD,KAAK,GAAG,IAAI,CAAC1C,UAAU,CAACC,GAAG,CAAC0C,SAAS,CAAC;IAC5C,IAAID,KAAK,IAAIA,KAAK,CAACkC,MAAM,KAAKlC,KAAK,CAACsF,KAAK,IAAItF,KAAK,CAACuF,OAAO,CAAC,EAAE;MAC3D,IAAIvF,KAAK,CAACkC,MAAM,CAAC,UAAU,CAAC,EAAE;QAC5B,OAAO,GAAG,IAAI,CAACuD,mBAAmB,CAACxF,SAAS,CAAC,cAAc;MAC7D;IACF;IACA,OAAO,EAAE;EACX;EAEAyF,sBAAsBA,CAAClE,aAAqB,EAAEvB,SAAiB;IAC7D,MAAM7B,cAAc,GAAG,IAAI,CAACd,UAAU,CAACC,GAAG,CAAC,WAAW,CAAc;IACpE,MAAMoI,aAAa,GAAGvH,cAAc,CAACmB,EAAE,CAACiC,aAAa,CAAc;IACnE,MAAMxB,KAAK,GAAG2F,aAAa,EAAEpI,GAAG,CAAC0C,SAAS,CAAC;IAC3C,OAAO,CAAC,EAAED,KAAK,IAAIA,KAAK,CAAC+D,OAAO,KAAK/D,KAAK,CAACsF,KAAK,IAAItF,KAAK,CAACuF,OAAO,CAAC,CAAC;EACrE;EAEAK,4BAA4BA,CAACpE,aAAqB,EAAEvB,SAAiB;IACnE,MAAM7B,cAAc,GAAG,IAAI,CAACd,UAAU,CAACC,GAAG,CAAC,WAAW,CAAc;IACpE,MAAMoI,aAAa,GAAGvH,cAAc,CAACmB,EAAE,CAACiC,aAAa,CAAc;IACnE,MAAMxB,KAAK,GAAG2F,aAAa,EAAEpI,GAAG,CAAC0C,SAAS,CAAC;IAC3C,IAAID,KAAK,IAAIA,KAAK,CAACkC,MAAM,KAAKlC,KAAK,CAACsF,KAAK,IAAItF,KAAK,CAACuF,OAAO,CAAC,EAAE;MAC3D,IAAIvF,KAAK,CAACkC,MAAM,CAAC,UAAU,CAAC,EAAE;QAC5B,OAAO,GAAG,IAAI,CAACuD,mBAAmB,CAACxF,SAAS,CAAC,cAAc;MAC7D;IACF;IACA,OAAO,EAAE;EACX;EAEQmE,oBAAoBA,CAAC3D,SAAgB;IAC3C,IAAI,CAACA,SAAS,IAAIA,SAAS,CAAChE,MAAM,KAAK,CAAC,EAAE;MACxC,OAAO,KAAK;IACd;IAEA,OAAOgE,SAAS,CAAC6C,IAAI,CAACzE,IAAI,IAAG;MAC3B,MAAMK,YAAY,GAAGL,IAAI,CAACK,YAAY,IAAI,EAAE;MAC5C,OAAOA,YAAY,CAAC5C,QAAQ,CAAC,SAAS,CAAC;IACzC,CAAC,CAAC;EACJ;EAEQmJ,mBAAmBA,CAACxF,SAAiB;IAC3C,MAAM4F,UAAU,GAA8B;MAC5C,cAAc,EAAE,eAAe;MAC/B,aAAa,EAAE,cAAc;MAC7B,cAAc,EAAE,cAAc;MAC9B,cAAc,EAAE;KACjB;IACD,OAAOA,UAAU,CAAC5F,SAAS,CAAC,IAAIA,SAAS;EAC3C;EAEA6F,cAAcA,CAAA;IACZ,MAAMC,mBAAmB,GAAG,IAAI,CAACC,sBAAsB,EAAE;IACzD,IAAID,mBAAmB,KAAK,CAAC,CAAC,EAAE;IAEhC,MAAM5D,OAAO,GAAI,IAAI,CAAC7E,UAAU,CAACC,GAAG,CAAC,WAAW,CAAe,CAACgC,EAAE,CAACwG,mBAAmB,CAAC,CAACxI,GAAG,CAAC,cAAc,CAAC;IAC3G,MAAM0I,YAAY,GAAG9D,OAAO,CAACpH,KAAK,IAAI,EAAE;IAExC,MAAMmL,eAAe,GAAGC,QAAQ,CAACC,cAAc,CAAC,cAAc,CAAwB;IACtF,IAAI,CAACF,eAAe,EAAE;IAEtB,MAAMG,cAAc,GAAGH,eAAe,CAACI,cAAc;IAGrD,MAAMC,QAAQ,GAAGN,YAAY,CAACf,SAAS,CAAC,CAAC,EAAEmB,cAAc,CAAC,GAC1C,SAAS,GACTJ,YAAY,CAACf,SAAS,CAACgB,eAAe,CAACM,YAAY,IAAIH,cAAc,CAAC;IAEtFlE,OAAO,CAACsE,QAAQ,CAACF,QAAQ,CAAC;IAC1BpE,OAAO,CAACjB,sBAAsB,EAAE;IAChCzB,UAAU,CAAC,MAAK;MACdyG,eAAe,CAACQ,KAAK,EAAE;MACvBR,eAAe,CAACS,iBAAiB,CAACN,cAAc,GAAG,CAAC,EAAEA,cAAc,GAAG,CAAC,CAAC;IAC3E,CAAC,EAAE,CAAC,CAAC;EACP;EAEAL,sBAAsBA,CAAA;IACpB,MAAMxG,cAAc,GAAG,IAAI,CAAClC,UAAU,CAACC,GAAG,CAAC,gBAAgB,CAAC,CAACxC,KAAK;IAClE,MAAM0F,SAAS,GAAG,IAAI,CAACnD,UAAU,CAACC,GAAG,CAAC,WAAW,CAAc;IAE/D,KAAK,IAAIqJ,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGnG,SAAS,CAAChE,MAAM,EAAEmK,CAAC,EAAE,EAAE;MACzC,MAAM/H,IAAI,GAAG4B,SAAS,CAAClB,EAAE,CAACqH,CAAC,CAAC,CAACrJ,GAAG,CAAC,cAAc,CAAC,CAACxC,KAAK;MACtD,IAAI8D,IAAI,KAAKW,cAAc,EAAE;QAC3B,OAAOoH,CAAC;MACV;IACF;IACA,OAAO,CAAC,CAAC;EACX;EAEAC,sBAAsBA,CAAA;IACpB,OAAO,IAAI,CAACvJ,UAAU,EAAEC,GAAG,CAAC,aAAa,CAAC,EAAExC,KAAK,KAAK,KAAK;EAC7D;EAEA;EACA+L,qBAAqBA,CAAA;IACrB,MAAMC,QAAQ,GAAG,IAAI,CAAC1L,eAAe,CAAC2L,QAAQ,CAAC,IAAI,CAAC9L,IAAI,CAAC;IACzD,IAAI,CAACoC,UAAU,CAACS,UAAU,CAAC;MACzBwC,YAAY,EAAEwG;KACf,CAAC;EACJ;EAEE;EACAE,kBAAkBA,CAAA;IAClB,MAAMC,WAAW,GAAG,iEAAiE;IACrF,MAAMC,cAAc,GAAG,IAAI,CAAC9L,eAAe,CAAC2L,QAAQ,CAACE,WAAW,CAAC;IAEjEvD,OAAO,CAACC,GAAG,CAAC,eAAe,EAAEsD,WAAW,CAAC;IACzCvD,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEuD,cAAc,CAAC;IAElD,IAAI,CAACjM,IAAI,GAAGgM,WAAW;IACvB,IAAI,CAAC5J,UAAU,CAACS,UAAU,CAAC;MACzBwC,YAAY,EAAE4G;KACf,CAAC;IAEF,OAAOA,cAAc;EACvB;EAGE;EACAC,kBAAkBA,CAACC,WAAmB;IACpC;IACA,IAAIN,QAAQ,GAAGM,WAAW;IAE1B;IACAN,QAAQ,GAAGA,QAAQ,CAAC3G,OAAO,CAAC,gBAAgB,EAAE,QAAQ,CAAC;IAEvD;IACA2G,QAAQ,GAAGA,QAAQ,CAAC3G,OAAO,CAAC,0BAA0B,EAAE,QAAQ,CAAC;IAEjE;IACA2G,QAAQ,GAAGA,QAAQ,CAAC3G,OAAO,CAAC,kBAAkB,EAAE,MAAM,CAAC;IAEvD;IACA2G,QAAQ,GAAGA,QAAQ,CAAC3G,OAAO,CAAC,6BAA6B,EAAE,UAAU,CAAC;IAEtE;IACA2G,QAAQ,GAAGA,QAAQ,CAAC3G,OAAO,CAAC,kBAAkB,EAAE,QAAQ,CAAC;IACzD2G,QAAQ,GAAGA,QAAQ,CAAC3G,OAAO,CAAC,kBAAkB,EAAE,SAAS,CAAC;IAC1D2G,QAAQ,GAAGA,QAAQ,CAAC3G,OAAO,CAAC,kBAAkB,EAAE,UAAU,CAAC;IAE3D;IACA2G,QAAQ,GAAGA,QAAQ,CAAC3G,OAAO,CAAC,aAAa,EAAE,IAAI,CAAC;IAEhD,OAAO2G,QAAQ;EACjB;;;;;;;cAzvBC1O,SAAS;QAAAiP,IAAA,GAAC,mBAAmB,EAAE;UAAEC,MAAM,EAAE;QAAK,CAAE;MAAA;;;;AA5BtC7N,uBAAuB,GAAA8N,UAAA,EAjBnCrP,SAAS,CAAC;EACTsP,QAAQ,EAAE,qBAAqB;EAC/BC,UAAU,EAAE,IAAI;EAChBC,OAAO,EAAE,CACPjP,WAAW,EACXC,mBAAmB,EACnBM,YAAY,EACZC,0BAA0B,EAC1BC,wBAAwB,EACxBC,yBAAyB,EACzBE,kBAAkB,EAAEC,sBAAsB,CAC3C;EACDqO,OAAO,EAAE,CAACtP,gBAAgB,CAAC;EAC3BuP,SAAS,EAAE,CAACxO,eAAe,CAAC;EAC5BoJ,QAAA,EAAAqF,oBAA+C;;CAEhD,CAAC,C,EACWpO,uBAAuB,CAuxBnC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}