<div class="inner-layout-container">
  <app-breadcrumb [data]="breadcrumbData"></app-breadcrumb>
  <div class="d-flex align-items-center mb-4">
    <h2 class="title mb-0">
      @if (isViewMode) {
      View Communication Template
      } @else if (isEditMode) {
      Edit Communication Template
      } @else {
      Create Communication Template
      }
    </h2>
    @if (!isViewMode) {
    <button tooltip="Design templates for different communication channels with variable
        support and multilingual capabilities" class="ms-3 p-0 border-0" type="button">
      <svg-icon src="assets/new/svgs/instruction-info.svg"></svg-icon>
    </button>
    }
  </div>

  @if (isLoading) {
  <div class="enc-card">
    <div class="card-content text-center p-5">
      <div class="spinner-border text-primary" role="status">
        <span class="visually-hidden">Loading...</span>
      </div>
      <p class="mt-3">Loading template data...</p>
    </div>
  </div>
  } @else {
  <div class="enc-card" [formGroup]="createForm">
    <!-- <div class="card-header">
        <div class="d-flex align-items-center">
          <h3>
            @if (isViewMode) {
              View Communication Template
            } @else if (isEditMode) {
              Edit Communication Template
            } @else {
              Create Communication Template
            }
          </h3>
          <button
            tooltip="Design templates for different communication channels with variable
            support and multilingual capabilities"
            class="info-icon ms-3"
            type="button"
          >
            <svg-icon src="assets/new/svgs/instruction-info.svg"></svg-icon>
          </button>
        </div>
      </div> -->
    <div class="card-content">
      <div class="row">
        <div class="col-md-12">
          <div class="form-control-group">
            <label class="form-label required">Channel Type</label>
            @if (isViewMode && currentTemplateData) {
            <div class="form-radio-group disabled">
              <label>
                <input type="radio" name="channelTypeView" id="channelTypeEmail" [value]="'email'"
                  [checked]="currentTemplateData.templateType?.toLowerCase() === 'email'" disabled />
                Email
              </label>
              <label>
                <input type="radio" name="channelTypeView" id="channelTypeSMS" [value]="'sms'"
                  [checked]="currentTemplateData.templateType?.toLowerCase() === 'sms'" disabled />
                SMS
              </label>
              <label>
                <input type="radio" name="channelTypeView" id="channelTypeLetter" [value]="'letter'"
                  [checked]="currentTemplateData.templateType?.toLowerCase() === 'letter'" disabled />
                Letter
              </label>
            </div>
            } @else {
            <div class="form-radio-group" [class.disabled]="isEditMode || isViewMode">
              <label>
                <input type="radio" name="channelType" id="channelTypeEmail" [value]="'email'"
                  formControlName="channelType" [disabled]="isEditMode || isViewMode" />
                Email
              </label>
              <label>
                <input type="radio" name="channelType" id="channelTypeSMS" [value]="'sms'" formControlName="channelType"
                  [disabled]="isEditMode || isViewMode" />
                SMS
              </label>
              <label>
                <input type="radio" name="channelType" id="channelTypeLetter" [value]="'letter'"
                  formControlName="channelType" [disabled]="isEditMode || isViewMode" />
                Letter
              </label>
            </div>
            }
          </div>
        </div>

        <div class="col-md-12">
          <div class="form-control-group">
            <label class="form-label required">Template Name</label>
            <input type="text" class="form-control" [class.is-invalid]="isFieldInvalid('templateName')"
              placeholder="Enter Template Name" id="templateName" formControlName="templateName"
              [readonly]="isEditMode || isViewMode" [class.form-control-readonly]="isEditMode || isViewMode" />
            @if (isFieldInvalid('templateName')) {
            <div class="form-error">
              {{ getFieldErrorMessage('templateName') }}
            </div>
            }
          </div>
        </div>
        @if ((isViewMode && currentTemplateData && (currentTemplateData.templateType?.toLowerCase() === 'email' ||
        currentTemplateData.templateType?.toLowerCase() === 'sms')) || (isEditMode && currentTemplateData &&
        (currentTemplateData.templateType?.toLowerCase() === 'email' || currentTemplateData.templateType?.toLowerCase()
        === 'sms')) || (!isViewMode && !isEditMode && (fValue?.channelType === 'email' || fValue?.channelType ===
        'sms'))) {
        <div class="col-md-12">
          <div class="form-control-group">
            <label class="form-label required">Allow Access from Account Details</label>
            @if (isViewMode && currentTemplateData) {
            <div class="form-radio-group disabled">
              <label>
                <input type="radio" name="allowAccessView" id="allowAccessYes" value="true"
                  [checked]="currentTemplateData.isAvailableInAccountDetails === true" disabled />
                Yes
              </label>
              <label>
                <input type="radio" name="allowAccessView" id="allowAccessNo" value="false"
                  [checked]="currentTemplateData.isAvailableInAccountDetails === false" disabled />
                No
              </label>
            </div>
            } @else {
            <div class="form-radio-group" [class.disabled]="isViewMode">
              <label>
                <input type="radio" name="allowAccessFromAccount" id="allowAccessYes" value="true"
                  formControlName="allowAccessFromAccount" [disabled]="isViewMode" />
                Yes
              </label>
              <label>
                <input type="radio" name="allowAccessFromAccount" id="allowAccessNo" value="false"
                  formControlName="allowAccessFromAccount" [disabled]="isViewMode" />
                No
              </label>
            </div>
            }
          </div>
        </div>
        }

        <div class="col-md-12">
          <div class="form-control-group">
            <label class="form-label required">Template Language</label>
            <div class="d-flex align-items-center justify-content-between">
              @if (isViewMode && currentTemplateData?.communicationTemplateDetails?.length > 0) {
              <div class="form-button-group">
                @for (detail of currentTemplateData.communicationTemplateDetails; let i = $index; track detail.language)
                {
                <div class="language-button-wrapper">
                  <button class="language-btn" [class.active]="selectedViewLanguageIndex === i"
                    (click)="selectViewLanguage(i)">
                    {{ detail.language }}
                  </button>
                </div>
                }
              </div>
              } @else {
              <div class="form-button-group" btnRadioGroup formControlName="activeLanguage">
                @for (lang of fValue?.languages; let i = $index; track lang.languageCode) {
                <div class="language-button-wrapper">
                  <button [btnRadio]="lang?.languageCode" id="template-language-{{ lang?.languageCode }}"
                    class="language-btn">
                    {{ lang?.languageName }}
                    @if (fValue?.languages?.length > 1 && !isViewMode) {
                    <span class="remove-language-btn" (click)="removeLanguage(i); $event.stopPropagation()"
                      title="Remove {{ lang?.languageName }}">
                      ×
                    </span>
                    }
                  </button>
                </div>
                }
              </div>
              }
              @if (!isViewMode) {
              <button class="btn btn-outline-primary" id="addLanguageBtn" (click)="openAddLangModal(addLangModal)">
                <svg-icon src="assets/new/svgs/language.svg" class="me-2"></svg-icon>
                <span>Add Language</span>
              </button>
              }
            </div>
          </div>
        </div>

        @if (createForm?.get('channelType')?.value === 'letter') {
        <div class="col-md-6">
          <div class="form-control-group">
            <label class="form-label">Letter Header</label>
            <ngx-editor-menu [editor]="editor"> </ngx-editor-menu>
            <ngx-editor [editor]="editor" [(ngModel)]="html" [disabled]="false"
              [placeholder]="'Type here...'" (ngModelChange)="onEditorContentChange()"></ngx-editor>
            <pre>{{html}}</pre>
          </div>

          <!-- Add this below your editor to show the Markdown output -->
          <div class="form-control-group mt-2">
            <label class="form-label">Markdown Output</label>
            <pre class="form-control">{{createForm.get('letterHeader')?.value}}</pre>
          </div>
        </div>
        <div class="col-md-6">
          <div class="form-control-group">
            <label class="form-label">Letter Footer</label>
            <ngx-editor-menu [editor]="editor2"> </ngx-editor-menu>
            <ngx-editor [editor]="editor2" [(ngModel)]="html2" [disabled]="false"
              [placeholder]="'Type here...'" (ngModelChange)="onFooterContentChange()"></ngx-editor>
            <pre>{{html2}}</pre>
          </div>

          <!-- Add this below your footer editor to show the Markdown output -->
          <div class="form-control-group mt-2">
            <label class="form-label">Footer Markdown Output</label>
            <pre class="form-control">{{createForm.get('letterFooter')?.value}}</pre>
          </div>
        </div>
        }

        @if (isViewMode && currentTemplateData?.communicationTemplateDetails?.length > 0) {
        @if (currentTemplateData.communicationTemplateDetails.length > 1) {
        <!-- div class="col-md-12">
                <div class="text-muted">
                  Viewing content in: <strong>{{ getSelectedLanguageDetail()?.language || 'English' }}</strong>
                </div>
              </div> -->
        }
        @if (currentTemplateData.templateType?.toLowerCase() === 'email') {
        <div class="col-md-12">
          <div class="form-control-group">
            <label class="form-label required">Subject Line</label>
            <input type="text" class="form-control form-control-readonly"
              [value]="getSelectedLanguageDetail()?.subject || ''" readonly />
          </div>
        </div>
        }
        <div class="col-md-12">
          <div class="form-control-group">
            <label class="form-label required">Template Body</label>
            <textarea class="form-control form-control-readonly" rows="7"
              [value]="getSelectedLanguageDetail()?.body || ''" readonly></textarea>
          </div>
        </div>
        <div class="col-md-12">
          <div class="form-control-group">
            <label class="form-label">Template Preview</label>
            <div appTemplatePreview [template]="getSelectedLanguageDetail()?.body || ''" [variables]="variables"></div>
          </div>
        </div>
        }

        @if (!isViewMode) {
        @for (lang of fValue?.languages; let i = $index; track lang.languageCode) {
        <ng-container [formArrayName]="'languages'">
          <ng-container [formGroupName]="i">
            @if (shouldShowLanguageFields(i)) {
            @if (fValue?.channelType === 'email') {
            <div class="col-md-12">
              <div class="form-control-group">
                <label class="form-label required">Subject Line</label>
                <input type="text" class="form-control" [class.is-invalid]="isLanguageFieldInvalid(i, 'emailSubject')"
                  placeholder="Enter Email Subject" id="emailSubject" formControlName="emailSubject" />
                @if (isLanguageFieldInvalid(i, 'emailSubject')) {
                <div class="form-error">
                  {{ getLanguageFieldErrorMessage(i, 'emailSubject') }}
                </div>
                }
              </div>
            </div>
            }
            <div class="col-md-12">
              <div class="form-control-group">
                <div class="d-flex align-items-center">
                  <label class="form-label required">Template Body</label>
                  <label class="form-label">
                    <button tooltip="Use <<Var>> to add variables that can be mapped to template variables"
                      class="ms-2 p-0 border-0" type="button">
                      <svg-icon src="assets/new/svgs/info_gry_icon.svg" width="18" height="18"></svg-icon>
                    </button>
                  </label>
                </div>
                <div class="position-relative">
                  <button type="button"
                    class="btn btn-primary rounded rounded-5 icon-btn position-absolute d-flex align-items-center justify-content-center add-var-btn"
                    (click)="insertVariable()" tooltip="Add Variable">
                    <svg-icon src="assets/new/svgs/plus.svg" width="16" height="16"></svg-icon>
                  </button>
                  <!-- <textarea class="form-control" [class.is-invalid]="isLanguageFieldInvalid(i, 'templateBody')"
                    placeholder="Enter Approved Template Content" id="templateBody" rows="7"
                    formControlName="templateBody" appRestrictHtmlTags></textarea> -->
                      <ngx-editor-menu [editor]="editor3"> </ngx-editor-menu>
            <ngx-editor [editor]="editor3" [ngModel]="html3" [disabled]="false"
              [placeholder]="'Type here...'"></ngx-editor>
                  <!-- [allowCopyPasteOnly]="fValue?.channelType === 'sms'" -->
                  @if (isLanguageFieldInvalid(i, 'templateBody')) {
                  <div class="form-error">
                    {{ getLanguageFieldErrorMessage(i, 'templateBody') }}
                  </div>
                  }
                  <!-- @if (fValue?.channelType === 'sms') {
                            <div class="text-muted mt-1 small">
                              <i>For SMS templates, copy, paste and delete operations only allowed.</i>
                            </div>
                          } -->
                </div>
              </div>
            </div>
            @if (fValue?.languages?.[i]?.templateBody && !isViewMode) {
            <div class="col-md-12">
              <div class="form-control-group">
                <div class="d-flex align-items-center justify-content-between">
                  <label class="form-label required">Template Variable Mapping</label>
                  <div class="variable-map-legends">
                    <label class="unmapped">Unmapped Variable</label>
                    <label class="mapped">Mapped Variable</label>
                  </div>
                </div>
                <div appTemplateVarConfig #templateVarConfig="appTemplateVarConfig"
                  [template]="fValue?.languages?.[i]?.templateBody"
                  (selectVar)="openMapVariableModal($event, mapVariableModal)"
                  (change)="updateTemplateValue($event, i)"></div>
              </div>
            </div>
            <div class="col-md-12">
              <div class="form-control-group">
                <div class="d-flex align-items-center justify-content-between">
                  <label class="form-label required">Message Preview Using Template</label>
                </div>
                <div appTemplatePreview [template]="fValue?.languages?.[i]?.templateBody" [variables]="variables"></div>
              </div>
            </div>
            }
            }
          </ng-container>
        </ng-container>
        }
        }


      </div>
    </div>
    <div class="card-footer">
      @if (isViewMode) {
      <button class="btn btn-outline-primary mw-150px"
        [routerLink]="['/communication/search-communication-templates']">Back to Search</button>
      } @else if (isEditMode) {
      <button class="btn btn-secondary mw-150px me-4" (click)="updateTemplate()">Update Template</button>
      <button class="btn btn-outline-primary mw-150px"
        [routerLink]="['/communication/search-communication-templates']">Cancel</button>
      } @else {
      <button class="btn btn-secondary mw-150px me-4" (click)="createTemplate()">Create Template</button>
      <button class="btn btn-outline-primary mw-150px"
        [routerLink]="['/communication/search-communication-templates']">Cancel</button>
      }
    </div>
  </div>
  }
</div>

<!-- Template: HTML Editor -->
<div class="form-control-group">
  <label class="form-label">Letter Header</label>
  <ngx-editor-menu [editor]="editor"> </ngx-editor-menu>
  <ngx-editor [editor]="editor" [(ngModel)]="html" [placeholder]="'Type here...'"
    (ngModelChange)="onEditorContentChange()">
  </ngx-editor>
  <pre>{{html}}</pre>
</div>

<!-- Template: Markdown Output -->
<div class="form-control-group mt-2">
  <label class="form-label">Markdown Output</label>
  <pre class="form-control">{{createForm.get('letterHeader')?.value}}</pre>
</div>

<ng-template #mapVariableModal>
  <div class="modal-header d-flex align-items-center justify-content-between">
    <h4 class="modal-title">Template Variable Mapping</h4>
    <img src="assets/new/svgs/side-drawer-close.svg" alt="Close" class="modal-close-btn"
      (click)="mapVarModalRef?.hide()" />
  </div>
  <div class="modal-body">
    <div class="form-control-group">
      <label class="form-label required">Mapping Fields</label>
      <ng-select class="form-ng-select" [items]="variables" bindLabel="name" bindValue="name"
        placeholder="Select Database Field" [(ngModel)]="selectedVariable.value" [clearable]="false"
        [searchable]="true">
      </ng-select>
    </div>
  </div>
  <div class="modal-footer justify-content-center">
    <button type="button" class="btn btn-success mw-150px" [disabled]="!selectedVariable?.value"
      (click)="assignVariable()">
      Map
    </button>
  </div>
</ng-template>

<ng-template #addLangModal>
  <div class="modal-header d-flex align-items-center justify-content-between">
    <h4 class="modal-title">Add New Language</h4>
    <img src="assets/new/svgs/side-drawer-close.svg" alt="Close" class="modal-close-btn"
      (click)="addLangModalRef?.hide()" />
  </div>
  <div class="modal-body">
    <div class="form-control-group">
      <label class="form-label required">Language</label>
      <ng-select class="form-ng-select" [items]="allLanguages" bindLabel="name" bindValue="code"
        placeholder="Select Language" [(ngModel)]="selectedLanguage" [clearable]="false" [searchable]="true">
      </ng-select>
    </div>
  </div>
  <div class="modal-footer justify-content-center">
    <button type="button" class="btn btn-success mw-150px" [disabled]="!selectedLanguage" (click)="addLanguage()">
      Add Language
    </button>
  </div>
</ng-template>
